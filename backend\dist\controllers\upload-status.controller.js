"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadStatusController = void 0;
const upload_status_service_1 = require("../services/upload-status.service");
const types_1 = require("../types");
class UploadStatusController {
    static async getImageStatus(req, res, next) {
        try {
            const { imageId } = req.params;
            const userId = parseInt(req.user.id);
            if (!imageId || isNaN(parseInt(imageId))) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的图片ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const status = await upload_status_service_1.UploadStatusService.getImageUploadStatus(parseInt(imageId), userId);
            if (!status) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '图片不存在或无权限访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            res.json({
                success: true,
                message: '获取图片状态成功',
                data: status
            });
        }
        catch (error) {
            console.error('获取图片状态失败:', error);
            next(error);
        }
    }
    static async getUploadHistory(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            const page = parseInt(req.query.page) || 1;
            const limit = Math.min(parseInt(req.query.limit) || 20, 100);
            const result = await upload_status_service_1.UploadStatusService.getUserUploadHistory(userId, page, limit);
            res.json({
                success: true,
                message: '获取上传历史成功',
                data: result
            });
        }
        catch (error) {
            console.error('获取上传历史失败:', error);
            next(error);
        }
    }
    static async getBackupStats(req, res, next) {
        try {
            const { imageId } = req.params;
            const userId = parseInt(req.user.id);
            if (!imageId || isNaN(parseInt(imageId))) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的图片ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const status = await upload_status_service_1.UploadStatusService.getImageUploadStatus(parseInt(imageId), userId);
            if (!status) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '图片不存在或无权限访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const stats = await upload_status_service_1.UploadStatusService.getBackupStats(parseInt(imageId));
            res.json({
                success: true,
                message: '获取备份统计成功',
                data: stats
            });
        }
        catch (error) {
            console.error('获取备份统计失败:', error);
            next(error);
        }
    }
    static async getProvidersOverview(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            const { UploadProviderService } = await Promise.resolve().then(() => __importStar(require('../services/upload-provider.service')));
            const { prisma } = await Promise.resolve().then(() => __importStar(require('../config/database')));
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { userLevel: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const providers = await UploadProviderService.getAvailableProviders(userId, user.userLevel);
            const providersWithStats = await Promise.all(providers.map(async (provider) => {
                const linkCount = await prisma.imageLink.count({
                    where: {
                        providerId: provider.id,
                        status: 'active'
                    }
                });
                const avgResponseTime = await prisma.imageLink.aggregate({
                    where: {
                        providerId: provider.id,
                        status: 'active',
                        responseTime: { not: null }
                    },
                    _avg: {
                        responseTime: true
                    }
                });
                return {
                    id: provider.id,
                    name: provider.name,
                    endpoint: provider.endpoint,
                    status: 'active',
                    priority: provider.isPremium ? 'high' : 'normal',
                    totalLinks: linkCount,
                    avgResponseTime: avgResponseTime._avg.responseTime || null,
                    maxFileSize: provider.maxFileSize,
                    supportedFormats: provider.supportedFormats
                };
            }));
            res.json({
                success: true,
                message: '获取接口概览成功',
                data: {
                    providers: providersWithStats,
                    summary: {
                        totalProviders: providers.length,
                        activeProviders: providers.length,
                        totalLinks: providersWithStats.reduce((sum, p) => sum + p.totalLinks, 0)
                    }
                }
            });
        }
        catch (error) {
            console.error('获取接口概览失败:', error);
            next(error);
        }
    }
}
exports.UploadStatusController = UploadStatusController;
//# sourceMappingURL=upload-status.controller.js.map