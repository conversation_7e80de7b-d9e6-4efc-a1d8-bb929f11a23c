"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUploadController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
class AdminUploadController {
    static async getAllUploadRecords(req, res, next) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = Math.min(parseInt(req.query.limit) || 20, 100);
            const offset = (page - 1) * limit;
            const userId = req.query.userId ? parseInt(req.query.userId) : undefined;
            const startDate = req.query.startDate;
            const endDate = req.query.endDate;
            const mimeType = req.query.mimeType;
            const status = req.query.status;
            const whereConditions = {};
            if (userId) {
                whereConditions.userId = userId;
            }
            if (startDate || endDate) {
                whereConditions.createdAt = {};
                if (startDate) {
                    whereConditions.createdAt.gte = new Date(startDate);
                }
                if (endDate) {
                    whereConditions.createdAt.lte = new Date(endDate);
                }
            }
            const imageConditions = {};
            if (mimeType) {
                imageConditions.mimeType = {
                    contains: mimeType,
                    mode: 'insensitive'
                };
            }
            if (status) {
                if (status === 'deleted') {
                    imageConditions.isDeleted = true;
                }
                else if (status === 'active') {
                    imageConditions.isDeleted = false;
                }
            }
            const fullWhereConditions = {
                ...whereConditions,
                ...(Object.keys(imageConditions).length > 0 && {
                    image: imageConditions
                })
            };
            const [uploads, totalCount] = await Promise.all([
                database_1.prisma.userImage.findMany({
                    where: fullWhereConditions,
                    include: {
                        user: {
                            select: {
                                id: true,
                                username: true,
                                email: true,
                                userLevel: true,
                                status: true
                            }
                        },
                        image: {
                            select: {
                                id: true,
                                publicId: true,
                                originalName: true,
                                fileHash: true,
                                fileSize: true,
                                mimeType: true,
                                width: true,
                                height: true,
                                systemUrl: true,
                                uploadStatus: true,
                                isDeleted: true,
                                createdAt: true,
                                imageLinks: {
                                    select: {
                                        id: true,
                                        externalUrl: true,
                                        status: true,
                                        provider: {
                                            select: {
                                                name: true,
                                                description: true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'desc'
                    },
                    skip: offset,
                    take: limit
                }),
                database_1.prisma.userImage.count({
                    where: fullWhereConditions
                })
            ]);
            const formattedUploads = uploads.map(upload => ({
                id: upload.id,
                userId: upload.userId,
                imageId: upload.imageId,
                isOriginalUploader: upload.isOriginalUploader,
                accessCount: upload.accessCount,
                createdAt: upload.createdAt.toISOString(),
                user: upload.user,
                image: upload.image ? {
                    ...upload.image,
                    fileSize: upload.image.fileSize.toString(),
                    createdAt: upload.image.createdAt.toISOString(),
                    links: upload.image.imageLinks.map((link) => ({
                        id: link.id,
                        url: link.externalUrl,
                        status: link.status,
                        provider: link.provider.description || link.provider.name
                    }))
                } : null
            }));
            res.json({
                success: true,
                data: {
                    uploads: formattedUploads,
                    pagination: {
                        page,
                        limit,
                        total: totalCount,
                        totalPages: Math.ceil(totalCount / limit),
                        hasNext: page * limit < totalCount,
                        hasPrev: page > 1
                    }
                }
            });
        }
        catch (error) {
            console.error('获取上传记录失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取上传记录失败',
                    timestamp: new Date().toISOString(),
                }
            });
        }
    }
    static async getUploadStatistics(req, res, next) {
        try {
            const days = parseInt(req.query.days) || 30;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            const [totalUploads, totalUsers, totalSize, recentUploads, uploadsByType, uploadsByUser, uploadsByDay] = await Promise.all([
                database_1.prisma.userImage.count(),
                database_1.prisma.userImage.groupBy({
                    by: ['userId'],
                    _count: true
                }).then(result => result.length),
                database_1.prisma.image.aggregate({
                    where: { isDeleted: false },
                    _sum: { fileSize: true }
                }).then(result => result._sum.fileSize || BigInt(0)),
                database_1.prisma.userImage.count({
                    where: {
                        createdAt: { gte: startDate }
                    }
                }),
                database_1.prisma.image.groupBy({
                    by: ['mimeType'],
                    where: { isDeleted: false },
                    _count: true,
                    orderBy: { _count: { mimeType: 'desc' } },
                    take: 10
                }),
                database_1.prisma.userImage.groupBy({
                    by: ['userId'],
                    _count: true,
                    orderBy: { _count: { userId: 'desc' } },
                    take: 10
                }).then(async (results) => {
                    const userIds = results.map(r => r.userId);
                    const users = await database_1.prisma.user.findMany({
                        where: { id: { in: userIds } },
                        select: { id: true, username: true, email: true }
                    });
                    return results.map(result => ({
                        userId: result.userId,
                        count: result._count,
                        user: users.find(u => u.id === result.userId)
                    }));
                }),
                database_1.prisma.$queryRaw `
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as count
          FROM user_images 
          WHERE created_at >= ${startDate}
          GROUP BY DATE(created_at)
          ORDER BY date DESC
        `
            ]);
            res.json({
                success: true,
                data: {
                    overview: {
                        totalUploads,
                        totalUsers,
                        totalSize: totalSize.toString(),
                        recentUploads,
                        period: `${days}天`
                    },
                    uploadsByType: uploadsByType.map(item => ({
                        mimeType: item.mimeType,
                        count: item._count
                    })),
                    uploadsByUser,
                    uploadsByDay
                }
            });
        }
        catch (error) {
            console.error('获取上传统计失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取上传统计失败',
                    timestamp: new Date().toISOString(),
                }
            });
        }
    }
    static async deleteUploadRecord(req, res, next) {
        try {
            const recordId = parseInt(req.params.recordId);
            const { reason } = req.body;
            if (!recordId || isNaN(recordId)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的记录ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const record = await database_1.prisma.userImage.findUnique({
                where: { id: recordId },
                include: {
                    image: true,
                    user: { select: { username: true, email: true } }
                }
            });
            if (!record) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '上传记录不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            await database_1.prisma.image.update({
                where: { id: record.imageId },
                data: {
                    isDeleted: true,
                    updatedAt: new Date()
                }
            });
            await database_1.prisma.systemEventLog.create({
                data: {
                    eventType: 'admin_delete_upload',
                    eventLevel: 'info',
                    eventMessage: `管理员删除用户上传记录`,
                    eventData: {
                        recordId,
                        imageId: record.imageId,
                        userId: record.userId,
                        username: record.user.username,
                        originalName: record.image.originalName,
                        reason: reason || '管理员删除',
                        adminId: req.user?.id
                    }
                }
            });
            res.json({
                success: true,
                message: '上传记录已删除',
                data: {
                    recordId,
                    imageId: record.imageId,
                    updatedAt: new Date().toISOString()
                }
            });
        }
        catch (error) {
            console.error('删除上传记录失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '删除上传记录失败',
                    timestamp: new Date().toISOString(),
                }
            });
        }
    }
    static async batchDeleteUploadRecords(req, res, next) {
        try {
            const { recordIds, reason } = req.body;
            if (!Array.isArray(recordIds) || recordIds.length === 0) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '请提供有效的记录ID数组',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const records = await database_1.prisma.userImage.findMany({
                where: { id: { in: recordIds } },
                include: {
                    image: true,
                    user: { select: { username: true, email: true } }
                }
            });
            if (records.length === 0) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '没有找到要删除的记录',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const imageIds = records.map(r => r.imageId);
            await database_1.prisma.image.updateMany({
                where: { id: { in: imageIds } },
                data: {
                    isDeleted: true,
                    updatedAt: new Date()
                }
            });
            await database_1.prisma.systemEventLog.create({
                data: {
                    eventType: 'admin_batch_delete_upload',
                    eventLevel: 'info',
                    eventMessage: `管理员批量删除用户上传记录`,
                    eventData: {
                        recordIds,
                        imageIds,
                        count: records.length,
                        reason: reason || '管理员批量删除',
                        adminId: req.user?.id,
                        records: records.map(r => ({
                            recordId: r.id,
                            userId: r.userId,
                            username: r.user.username,
                            originalName: r.image.originalName
                        }))
                    }
                }
            });
            res.json({
                success: true,
                message: `已删除 ${records.length} 条上传记录`,
                data: {
                    deletedCount: records.length,
                    deletedRecords: records.map(r => ({
                        recordId: r.id,
                        imageId: r.imageId,
                        originalName: r.image.originalName
                    })),
                    updatedAt: new Date().toISOString()
                }
            });
        }
        catch (error) {
            console.error('批量删除上传记录失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '批量删除上传记录失败',
                    timestamp: new Date().toISOString(),
                }
            });
        }
    }
}
exports.AdminUploadController = AdminUploadController;
//# sourceMappingURL=admin-upload.controller.js.map