export interface HealthCheckResult {
    linkId: number;
    isHealthy: boolean;
    responseTime?: number;
    statusCode?: number;
    error?: string;
}
export declare class LinkHealthCheckService {
    private static readonly TIMEOUT;
    private static readonly MAX_CONCURRENT_CHECKS;
    private static readonly CHECK_INTERVAL;
    private static checkTimer;
    static startPeriodicHealthCheck(): void;
    static stopPeriodicHealthCheck(): void;
    static performHealthCheck(): Promise<void>;
    private static checkSingleLink;
    private static updateLinkStatus;
    static checkImageLinks(imageId: number): Promise<HealthCheckResult[]>;
    static getHealthStats(): Promise<any>;
    private static chunkArray;
    private static sleep;
}
//# sourceMappingURL=link-health-check.service.d.ts.map