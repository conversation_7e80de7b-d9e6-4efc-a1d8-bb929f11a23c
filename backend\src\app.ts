// LoftChat Backend Server
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { config } from './config/env';
import { connectDatabase, disconnectDatabase } from './config/database';
import { connectRedis, disconnectRedis } from './config/redis';
import { SocketService } from './config/socket';
import { LogService } from './services/log.service';
import { SystemMonitorService } from './services/system-monitor.service';
import { QueueService } from './services/queue.service';
import { LinkHealthCheckService } from './services/link-health-check.service';
import authRoutes from './routes/auth.routes';
import uploadRoutes from './routes/upload.routes';
import adminRoutes from './routes/admin.routes';
import userRoutes from './routes/user.routes';
import levelApplicationRoutes from './routes/levelApplication.routes';
import invitationCodeRoutes from './routes/invitation-code.routes';
import testRoutes from './routes/test.routes';
import userProviderRoutes from './routes/user-provider.routes';
import imageProxyRoutes from './routes/image-proxy.routes';
import imageRedirectConfigRoutes from './routes/image-redirect-config.routes';
import adminUploadRoutes from './routes/admin-upload.routes';

// 全局 BigInt 序列化支持
(BigInt.prototype as any).toJSON = function() {
  return Number(this);
};

// 创建Express应用
const app = express();

// 最早的请求日志
app.use((req, res, next) => {
  console.log(`📥 收到请求: ${req.method} ${req.path}`);
  next();
});

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: false, // 开发环境下禁用CSP
}));

// CORS配置
app.use(cors({
  origin: config.frontend.url,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// 速率限制
if (config.security.enableRateLimiting) {
  const limiter = rateLimit({
    windowMs: config.security.rateLimitWindow,
    max: config.security.rateLimitMax,
    message: {
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString(),
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  
  app.use('/api/', limiter);
}

// 请求日志中间件
app.use((req, res, next) => {
  // 记录所有请求
  console.log(`🌐 ${req.method} ${req.path}`);
  if (req.path.startsWith('/api/upload/')) {
    console.log('Content-Type:', req.get('Content-Type'));
    console.log('Content-Length:', req.get('Content-Length'));
  }
  next();
});

// 解析JSON和URL编码的请求体（跳过文件上传请求）
app.use((req, res, next) => {
  // 跳过文件上传路由的 JSON 解析
  if (req.path.startsWith('/api/upload/') && req.method === 'POST') {
    return next();
  }
  express.json({ limit: '10mb' })(req, res, next);
});

app.use((req, res, next) => {
  // 跳过文件上传路由的 URL 编码解析
  if (req.path.startsWith('/api/upload/') && req.method === 'POST') {
    return next();
  }
  express.urlencoded({ extended: true, limit: '10mb' })(req, res, next);
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.env,
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API路由
app.get('/api', (req, res) => {
  res.json({
    message: 'LoftChat 智能图片上传管理分发系统 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      upload: '/api/upload',
      images: '/api/images',
      admin: '/api/admin',
    }
  });
});



// 注册路由
app.use('/api/auth', authRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/admin/invitation-codes', invitationCodeRoutes);
app.use('/api/admin/uploads', adminUploadRoutes);
app.use('/api/user', userRoutes);
app.use('/api/level-application', levelApplicationRoutes);
app.use('/api/user-providers', userProviderRoutes);
app.use('/api/test', testRoutes);

// 图片代理路由（公开访问）
app.use('/image', imageProxyRoutes);

// 图片跳转配置路由（需要认证）
app.use('/api/image-redirect-config', imageRedirectConfigRoutes);

// 公开的邀请码验证路由（不需要认证）
app.get('/api/invitation-codes/validate/:code', async (req, res, next): Promise<void> => {
  try {
    const { code } = req.params;

    console.log('🔍 公开路由收到邀请码验证请求:');
    console.log(`   原始参数: ${JSON.stringify(req.params)}`);
    console.log(`   邀请码: "${code}"`);
    console.log(`   邀请码长度: ${code?.length}`);
    console.log(`   邀请码字节: [${code ? Array.from(code).map(c => c.charCodeAt(0)).join(', ') : 'null'}]`);

    if (!code) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: '邀请码不能为空',
          timestamp: new Date().toISOString()
        }
      });
      return;
    }

    // 导入 InvitationCodeService
    const { InvitationCodeService } = await import('./services/invitation-code.service');
    console.log('📡 调用 InvitationCodeService.validateCode...');
    const validation = await InvitationCodeService.validateCode(code);
    console.log('📊 验证结果:', JSON.stringify(validation, null, 2));

    res.json({
      success: true,
      data: validation,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('验证邀请码失败:', error);
    next(error);
  }
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: '请求的资源不存在',
      path: req.originalUrl,
      timestamp: new Date().toISOString(),
    }
  });
});

// 全局错误处理中间件
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ 服务器错误:', error);

  // 开发环境下返回详细错误信息
  const isDevelopment = config.env === 'development';
  
  res.status(error.status || 500).json({
    success: false,
    error: {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message || '服务器内部错误',
      timestamp: new Date().toISOString(),
      ...(isDevelopment && { stack: error.stack }),
    }
  });
});

// 启动服务器
async function startServer(): Promise<void> {
  try {
    // 连接数据库
    await connectDatabase();

    // 连接Redis
    await connectRedis();

    // 初始化队列服务
    await QueueService.initialize();
    console.log('🔄 队列服务已初始化');

    // 创建HTTP服务器
    const server = createServer(app);

    // 初始化Socket.IO服务
    const socketService = SocketService.getInstance(server);

    // 初始化日志服务
    LogService.initialize(socketService);

    // 初始化系统监控服务
    const systemMonitor = new SystemMonitorService(socketService);
    systemMonitor.startMonitoring(30000); // 每30秒采集一次数据

    // 启动链接健康检查服务
    LinkHealthCheckService.startPeriodicHealthCheck();

    // 启动HTTP服务器
    server.listen(config.port, () => {
      console.log(`🚀 服务器启动成功！`);
      console.log(`📍 地址: http://localhost:${config.port}`);
      console.log(`🌍 环境: ${config.env}`);
      console.log(`📊 健康检查: http://localhost:${config.port}/health`);
      console.log(`🔗 API文档: http://localhost:${config.port}/api`);
      console.log(`🔌 Socket.IO 实时日志服务已启动`);
      console.log(`📈 系统监控服务已启动`);
      console.log(`🔍 链接健康检查服务已启动`);
    });

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n📡 收到 ${signal} 信号，开始优雅关闭...`);

      server.close(async () => {
        console.log('🔌 HTTP服务器已关闭');

        try {
          // 停止系统监控服务
          systemMonitor.stopMonitoring();
          console.log('📈 系统监控服务已停止');

          // 停止链接健康检查服务
          LinkHealthCheckService.stopPeriodicHealthCheck();
          console.log('🔍 链接健康检查服务已停止');

          // 关闭Socket.IO服务
          socketService.close();
          console.log('🔌 Socket.IO服务已关闭');

          // 关闭队列服务
          await QueueService.shutdown();
          console.log('🔄 队列服务已关闭');

          await disconnectDatabase();
          await disconnectRedis();
          console.log('✅ 所有连接已关闭');
          process.exit(0);
        } catch (error) {
          console.error('❌ 关闭连接时出错:', error);
          process.exit(1);
        }
      });
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  console.error('Promise:', promise);
  process.exit(1);
});

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

export { app, startServer };
