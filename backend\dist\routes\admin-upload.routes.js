"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const admin_upload_controller_1 = require("../controllers/admin-upload.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.requestId);
router.use(auth_middleware_1.recordIP);
router.use(auth_middleware_1.authenticateToken);
router.use(auth_middleware_1.requireAdmin);
router.get('/', (0, express_validator_1.query)('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'), (0, express_validator_1.query)('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'), (0, express_validator_1.query)('userId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('用户ID必须是正整数'), (0, express_validator_1.query)('startDate')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式不正确'), (0, express_validator_1.query)('endDate')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式不正确'), (0, express_validator_1.query)('mimeType')
    .optional()
    .isString()
    .withMessage('文件类型必须是字符串'), (0, express_validator_1.query)('status')
    .optional()
    .isIn(['active', 'deleted'])
    .withMessage('状态必须是 active 或 deleted'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.getAllUploadRecords);
router.get('/statistics', (0, express_validator_1.query)('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('统计天数必须在1-365之间'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.getUploadStatistics);
router.delete('/:recordId', (0, express_validator_1.param)('recordId')
    .isInt({ min: 1 })
    .withMessage('记录ID必须是正整数'), (0, express_validator_1.body)('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('删除原因不能超过500字符'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.deleteUploadRecord);
router.get('/image/:imageId/proxy-config', (0, express_validator_1.param)('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.getImageProxyConfig);
router.put('/image/:imageId/proxy-config', (0, express_validator_1.param)('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'), (0, express_validator_1.body)('redirectStrategy')
    .optional()
    .isIn(['auto', 'provider', 'priority'])
    .withMessage('重定向策略必须是 auto, provider 或 priority'), (0, express_validator_1.body)('preferredProviderId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('首选提供商ID必须是正整数'), (0, express_validator_1.body)('fallbackStrategy')
    .optional()
    .isIn(['auto', 'priority'])
    .withMessage('回退策略必须是 auto 或 priority'), (0, express_validator_1.body)('isEnabled')
    .optional()
    .isBoolean()
    .withMessage('启用状态必须是布尔值'), (0, express_validator_1.body)('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('修改原因不能超过500字符'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.updateImageProxyConfig);
router.put('/link/:linkId/status', (0, express_validator_1.param)('linkId')
    .isInt({ min: 1 })
    .withMessage('链接ID必须是正整数'), (0, express_validator_1.body)('status')
    .isIn(['active', 'inactive', 'failed'])
    .withMessage('状态必须是 active, inactive 或 failed'), (0, express_validator_1.body)('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('修改原因不能超过500字符'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.updateImageLinkStatus);
router.post('/batch-delete', (0, express_validator_1.body)('recordIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('记录ID数组长度必须在1-100之间'), (0, express_validator_1.body)('recordIds.*')
    .isInt({ min: 1 })
    .withMessage('每个记录ID必须是正整数'), (0, express_validator_1.body)('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('删除原因不能超过500字符'), validation_middleware_1.validateRequest, admin_upload_controller_1.AdminUploadController.batchDeleteUploadRecords);
exports.default = router;
//# sourceMappingURL=admin-upload.routes.js.map