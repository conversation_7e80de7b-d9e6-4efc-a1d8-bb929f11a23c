import { Job } from 'bullmq';
export interface UploadJobData {
    imageId: number;
    userId: number;
    filePath: string;
    providerId?: number | undefined;
    primaryProviderId?: number | undefined;
    fileInfo: {
        originalName: string;
        mimeType: string;
        size: number;
        hash: string;
    };
}
export interface ImageProcessingJobData {
    imageId: number;
    filePath: string;
    operations: {
        generateThumbnails?: boolean;
        compress?: boolean;
        watermark?: boolean;
    };
}
export declare class QueueService {
    private static uploadQueue;
    private static imageProcessingQueue;
    private static uploadWorker;
    private static imageProcessingWorker;
    static initialize(): Promise<void>;
    static addUploadJob(data: UploadJobData): Promise<Job<UploadJobData>>;
    static addImageProcessingJob(data: ImageProcessingJobData): Promise<Job<ImageProcessingJobData>>;
    private static processUploadJob;
    private static processImageProcessingJob;
    private static generateThumbnails;
    private static compressImage;
    private static addWatermark;
    private static setupEventListeners;
    static getQueueStats(): Promise<{
        upload: any;
        imageProcessing: any;
    }>;
    static cleanQueues(): Promise<void>;
    static shutdown(): Promise<void>;
}
//# sourceMappingURL=queue.service.d.ts.map