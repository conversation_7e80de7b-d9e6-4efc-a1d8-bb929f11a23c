"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadStatusService = void 0;
const database_1 = require("../config/database");
class UploadStatusService {
    static async getImageUploadStatus(imageId, userId) {
        try {
            const userImage = await database_1.prisma.userImage.findUnique({
                where: {
                    userId_imageId: {
                        userId,
                        imageId
                    }
                }
            });
            if (!userImage) {
                return null;
            }
            const image = await database_1.prisma.image.findUnique({
                where: { id: imageId },
                include: {
                    imageLinks: {
                        include: {
                            provider: {
                                select: {
                                    id: true,
                                    name: true,
                                    priority: true
                                }
                            }
                        },
                        orderBy: {
                            provider: {
                                priority: 'asc'
                            }
                        }
                    }
                }
            });
            if (!image) {
                return null;
            }
            const activeLinks = image.imageLinks.filter(link => link.status === 'active');
            const primaryLink = activeLinks.length > 0 ? activeLinks[0] : null;
            const backupLinks = activeLinks.slice(1);
            const totalProviders = await database_1.prisma.uploadProvider.count({
                where: { status: 'active' }
            });
            const completedBackups = activeLinks.length;
            const backupProgress = {
                completed: completedBackups,
                total: totalProviders,
                percentage: totalProviders > 0 ? Math.round((completedBackups / totalProviders) * 100) : 0
            };
            return {
                imageId: image.id,
                publicId: image.publicId || image.id.toString(),
                originalName: image.originalName,
                uploadStatus: image.uploadStatus,
                systemUrl: image.systemUrl,
                primaryLink: primaryLink ? {
                    provider: primaryLink.provider.name,
                    url: primaryLink.externalUrl,
                    status: primaryLink.status,
                    responseTime: primaryLink.responseTime ?? undefined
                } : undefined,
                backupLinks: backupLinks.map(link => ({
                    provider: link.provider.name,
                    url: link.externalUrl,
                    status: link.status,
                    responseTime: link.responseTime ?? undefined
                })),
                backupProgress,
                createdAt: image.createdAt.toISOString()
            };
        }
        catch (error) {
            console.error('获取图片上传状态失败:', error);
            return null;
        }
    }
    static async getUserUploadHistory(userId, page = 1, limit = 20) {
        try {
            const offset = (page - 1) * limit;
            const [userImages, total] = await Promise.all([
                database_1.prisma.userImage.findMany({
                    where: { userId },
                    include: {
                        image: {
                            include: {
                                imageLinks: {
                                    include: {
                                        provider: {
                                            select: {
                                                id: true,
                                                name: true,
                                                priority: true
                                            }
                                        }
                                    },
                                    orderBy: {
                                        provider: {
                                            priority: 'asc'
                                        }
                                    }
                                }
                            }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip: offset,
                    take: limit
                }),
                database_1.prisma.userImage.count({ where: { userId } })
            ]);
            const totalProviders = await database_1.prisma.uploadProvider.count({
                where: { status: 'active' }
            });
            const uploads = userImages.map(userImage => {
                const image = userImage.image;
                const activeLinks = image.imageLinks.filter(link => link.status === 'active');
                const primaryLink = activeLinks.length > 0 ? activeLinks[0] : null;
                const backupLinks = activeLinks.slice(1);
                const completedBackups = activeLinks.length;
                const backupProgress = {
                    completed: completedBackups,
                    total: totalProviders,
                    percentage: totalProviders > 0 ? Math.round((completedBackups / totalProviders) * 100) : 0
                };
                return {
                    imageId: image.id,
                    publicId: image.publicId || image.id.toString(),
                    originalName: image.originalName,
                    uploadStatus: image.uploadStatus,
                    systemUrl: image.systemUrl,
                    primaryLink: primaryLink ? {
                        provider: primaryLink.provider.name,
                        url: primaryLink.externalUrl,
                        status: primaryLink.status,
                        responseTime: primaryLink.responseTime ?? undefined
                    } : undefined,
                    backupLinks: backupLinks.map(link => ({
                        provider: link.provider.name,
                        url: link.externalUrl,
                        status: link.status,
                        responseTime: link.responseTime ?? undefined
                    })),
                    backupProgress,
                    createdAt: image.createdAt.toISOString()
                };
            });
            return {
                uploads,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
        }
        catch (error) {
            console.error('获取用户上传历史失败:', error);
            return {
                uploads: [],
                pagination: { page, limit, total: 0, totalPages: 0 }
            };
        }
    }
    static async getBackupStats(imageId) {
        try {
            const [totalProviders, imageLinks] = await Promise.all([
                database_1.prisma.uploadProvider.count({ where: { status: 'active' } }),
                database_1.prisma.imageLink.findMany({
                    where: { imageId },
                    select: { status: true }
                })
            ]);
            const activeLinks = imageLinks.filter(link => link.status === 'active').length;
            const failedLinks = imageLinks.filter(link => link.status === 'failed').length;
            const pendingUploads = Math.max(0, totalProviders - imageLinks.length);
            return {
                totalProviders,
                activeLinks,
                failedLinks,
                pendingUploads
            };
        }
        catch (error) {
            console.error('获取备份统计失败:', error);
            return {
                totalProviders: 0,
                activeLinks: 0,
                failedLinks: 0,
                pendingUploads: 0
            };
        }
    }
}
exports.UploadStatusService = UploadStatusService;
//# sourceMappingURL=upload-status.service.js.map