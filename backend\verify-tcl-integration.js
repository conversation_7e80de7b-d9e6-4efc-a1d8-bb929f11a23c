/**
 * 验证TCL接口在系统中的集成状态
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyTCLIntegration() {
  console.log('🔍 验证TCL接口在系统中的集成状态...\n');

  try {
    // 1. 检查TCL提供商配置
    console.log('1️⃣ 检查TCL提供商配置...');
    const tclProvider = await prisma.uploadProvider.findFirst({
      where: { name: 'TCL' }
    });

    if (tclProvider) {
      console.log('✅ TCL提供商配置存在');
      console.log('📋 配置详情:');
      console.log(`   ID: ${tclProvider.id}`);
      console.log(`   名称: ${tclProvider.name}`);
      console.log(`   端点: ${tclProvider.endpoint}`);
      console.log(`   状态: ${tclProvider.status}`);
      console.log(`   优先级: ${tclProvider.priority}`);
      console.log(`   所需等级: ${tclProvider.requiredLevel}`);
      console.log(`   是否高级: ${tclProvider.isPremium}`);
    } else {
      console.log('❌ TCL提供商配置不存在');
      return;
    }

    // 2. 检查等级可见性配置
    console.log('\n2️⃣ 检查等级可见性配置...');
    const visibilityConfigs = await prisma.levelProviderVisibility.findMany({
      where: { providerId: tclProvider.id },
      orderBy: { level: 'asc' }
    });

    if (visibilityConfigs.length > 0) {
      console.log('✅ 等级可见性配置存在');
      visibilityConfigs.forEach(config => {
        console.log(`   ${config.level}: ${config.isVisible ? '✅ 可见' : '❌ 不可见'} (显示顺序: ${config.displayOrder})`);
      });
    } else {
      console.log('❌ 等级可见性配置不存在');
    }

    // 3. 检查所有上传提供商
    console.log('\n3️⃣ 检查所有上传提供商...');
    const allProviders = await prisma.uploadProvider.findMany({
      orderBy: { priority: 'asc' }
    });

    console.log('📋 系统中的所有上传提供商:');
    allProviders.forEach(provider => {
      const statusIcon = provider.status === 'active' ? '✅' : '❌';
      console.log(`   ${statusIcon} ${provider.name} (ID: ${provider.id}, 优先级: ${provider.priority}, 状态: ${provider.status})`);
    });

    // 4. 检查用户可用的提供商（以free用户为例）
    console.log('\n4️⃣ 检查free用户可用的提供商...');
    const freeUserProviders = await prisma.levelProviderVisibility.findMany({
      where: {
        level: 'free',
        isVisible: true,
      },
      include: {
        provider: true
      },
      orderBy: {
        displayOrder: 'asc'
      }
    });

    console.log('📋 free用户可用的提供商:');
    freeUserProviders.forEach(config => {
      if (config.provider && config.provider.status === 'active') {
        console.log(`   ✅ ${config.provider.name} (ID: ${config.provider.id})`);
      }
    });

    // 5. 验证TCL接口是否在可用列表中
    console.log('\n5️⃣ 验证TCL接口可用性...');
    const tclAvailable = freeUserProviders.some(config => 
      config.provider && config.provider.name === 'TCL' && config.provider.status === 'active'
    );

    if (tclAvailable) {
      console.log('✅ TCL接口对free用户可用');
    } else {
      console.log('❌ TCL接口对free用户不可用');
    }

    // 6. 检查最近的上传日志
    console.log('\n6️⃣ 检查最近的上传日志...');
    const recentUploads = await prisma.uploadLog.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        image: true
      }
    });

    if (recentUploads.length > 0) {
      console.log('📋 最近的上传记录:');
      recentUploads.forEach(log => {
        console.log(`   📤 用户${log.userId} - ${log.action} - ${log.createdAt.toISOString()}`);
      });
    } else {
      console.log('ℹ️ 暂无上传记录');
    }

    // 7. 检查图片链接记录
    console.log('\n7️⃣ 检查图片链接记录...');
    const imageLinks = await prisma.imageLink.findMany({
      where: { providerId: tclProvider.id },
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        provider: true
      }
    });

    if (imageLinks.length > 0) {
      console.log('📋 TCL接口的图片链接记录:');
      imageLinks.forEach(link => {
        console.log(`   🔗 图片${link.imageId} - ${link.status} - ${link.externalUrl}`);
      });
    } else {
      console.log('ℹ️ 暂无TCL接口的图片链接记录');
    }

    console.log('\n🎉 TCL接口集成验证完成！');

    // 8. 生成集成状态报告
    console.log('\n📊 集成状态报告:');
    console.log('=' .repeat(50));
    console.log(`✅ TCL提供商配置: ${tclProvider ? '已配置' : '未配置'}`);
    console.log(`✅ 等级可见性: ${visibilityConfigs.length > 0 ? '已配置' : '未配置'}`);
    console.log(`✅ 对free用户可用: ${tclAvailable ? '是' : '否'}`);
    console.log(`✅ 接口状态: ${tclProvider?.status || '未知'}`);
    console.log(`✅ 优先级: ${tclProvider?.priority || '未知'}`);
    console.log(`✅ 端点地址: ${tclProvider?.endpoint || '未知'}`);
    console.log('=' .repeat(50));

    if (tclProvider && tclProvider.status === 'active' && tclAvailable) {
      console.log('🎯 结论: TCL接口已完全集成并可正常使用！');
    } else {
      console.log('⚠️ 结论: TCL接口集成存在问题，需要检查配置');
    }

  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行验证
if (require.main === module) {
  verifyTCLIntegration()
    .then(() => {
      console.log('\n✨ 验证脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 验证脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { verifyTCLIntegration };
