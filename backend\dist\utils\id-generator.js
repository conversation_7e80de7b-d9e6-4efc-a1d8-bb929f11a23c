"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdGenerator = void 0;
const crypto_1 = __importDefault(require("crypto"));
class IdGenerator {
    static generateImageId() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
        let result = '';
        for (let i = 0; i < 16; i++) {
            const randomIndex = crypto_1.default.randomInt(0, chars.length);
            result += chars[randomIndex];
        }
        return result;
    }
    static async generateUniqueImageId(checkExists) {
        let attempts = 0;
        const maxAttempts = 10;
        while (attempts < maxAttempts) {
            const id = this.generateImageId();
            const exists = await checkExists(id);
            if (!exists) {
                return id;
            }
            attempts++;
        }
        throw new Error('无法生成唯一的图片ID，请重试');
    }
    static validateImageId(id) {
        if (id.length !== 16) {
            return false;
        }
        const validChars = /^[ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789]+$/;
        return validChars.test(id);
    }
    static generateShortId() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            const randomIndex = crypto_1.default.randomInt(0, chars.length);
            result += chars[randomIndex];
        }
        return result;
    }
}
exports.IdGenerator = IdGenerator;
//# sourceMappingURL=id-generator.js.map