export interface UploadResult {
    success: boolean;
    url?: string;
    error?: string;
    responseTime?: number;
    providerId: number;
    providerName: string;
}
export interface ProviderConfig {
    id: number;
    name: string;
    endpoint: string;
    apiKey?: string;
    config?: any;
    maxFileSize: number;
    supportedFormats: string[];
    isPremium: boolean;
}
export declare class UploadProviderService {
    static getAvailableProviders(userId: number, userLevel: string): Promise<ProviderConfig[]>;
    static uploadToProvider(provider: ProviderConfig, fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult>;
    private static uploadToImgur;
    private static uploadToCloudinary;
    static getProviderById(providerId: number): Promise<ProviderConfig | null>;
    private static uploadToQiniu;
    private static uploadToTCL;
    private static uploadToShuidi;
    private static uploadToCustomProvider;
    private static uploadToGenericProvider;
    static uploadToMultipleProviders(providers: ProviderConfig[], fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult[]>;
    static saveUploadResults(imageId: number, results: UploadResult[]): Promise<void>;
    static checkProviderHealth(provider: ProviderConfig): Promise<boolean>;
    private static getNestedProperty;
    static validateProviderConfig(provider: ProviderConfig): {
        valid: boolean;
        errors: string[];
    };
}
//# sourceMappingURL=upload-provider.service.d.ts.map