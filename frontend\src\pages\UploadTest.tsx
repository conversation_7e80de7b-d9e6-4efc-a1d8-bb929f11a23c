import React, { useState } from 'react';
import { Card, Card<PERSON>eader, CardTitle, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { FileUpload } from '../components/upload/FileUpload';
import { UploadHistory } from '../components/upload/UploadHistory';

export function UploadTest() {
  const [activeTab, setActiveTab] = useState<'upload' | 'history' | 'guide'>('upload');

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          多接口备份上传系统
        </h1>
        <p className="text-gray-600">
          一次上传，多接口静默备份，永久有效的代理链接
        </p>
      </div>

      {/* 功能特性展示 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="font-semibold text-green-800 mb-1">即时可用</h3>
            <p className="text-sm text-green-700">主接口完成后立即获得可用链接</p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
            </div>
            <h3 className="font-semibold text-blue-800 mb-1">静默备份</h3>
            <p className="text-sm text-blue-700">后台自动备份到所有可用接口</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-purple-50">
          <CardContent className="p-4 text-center">
            <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-purple-800 mb-1">智能配置</h3>
            <p className="text-sm text-purple-700">为每张图片单独配置跳转策略</p>
          </CardContent>
        </Card>
      </div>

      {/* 标签页导航 */}
      <Card>
        <CardContent className="p-0">
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab('upload')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'upload'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              📤 文件上传
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              📋 上传历史
            </button>
            <button
              onClick={() => setActiveTab('guide')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'guide'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              📖 使用指南
            </button>
          </div>
        </CardContent>
      </Card>

      {/* 标签页内容 */}
      {activeTab === 'upload' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>多接口备份上传</CardTitle>
              <p className="text-sm text-gray-600">
                上传文件后，系统会立即上传到主接口（用户可见），同时在后台静默上传到所有其他接口作为备份。
              </p>
            </CardHeader>
            <CardContent>
              <FileUpload onUploadSuccess={() => {
                // 上传成功后可以做一些处理
                console.log('上传成功');
              }} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>系统特性</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-green-700 mb-3">✅ 用户体验</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li className="flex items-start space-x-2">
                      <span className="text-green-500 mt-0.5">•</span>
                      <span>只显示主接口上传状态，用户无需等待</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-green-500 mt-0.5">•</span>
                      <span>主接口完成后立即可用，获得永久代理链接</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-green-500 mt-0.5">•</span>
                      <span>相同文件自动去重，直接返回已有链接</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-green-500 mt-0.5">•</span>
                      <span>支持为每张图片单独配置跳转策略</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-blue-700 mb-3">🔄 后台处理</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li className="flex items-start space-x-2">
                      <span className="text-blue-500 mt-0.5">•</span>
                      <span>静默上传到所有其他可用接口</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-blue-500 mt-0.5">•</span>
                      <span>自动故障转移，确保链接永不失效</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-blue-500 mt-0.5">•</span>
                      <span>智能链接选择，优先使用最快接口</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-blue-500 mt-0.5">•</span>
                      <span>备份进度跟踪，状态完全透明</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'history' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>上传历史与跳转设置</CardTitle>
              <p className="text-sm text-gray-600">
                查看上传历史，为每张图片配置专属的跳转策略。点击"跳转设置"按钮可以为单张图片配置跳转偏好。
              </p>
            </CardHeader>
          </Card>
          
          <UploadHistory />
        </div>
      )}

      {activeTab === 'guide' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>使用指南</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">🎯 设置跳转偏好</h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">全局设置</h4>
                  <p className="text-sm text-blue-800 mb-2">
                    进入 <Badge variant="outline">设置</Badge> → <Badge variant="outline">上传设置</Badge> → <Badge variant="outline">图片跳转配置</Badge>
                  </p>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• <strong>自动最优模式</strong>：系统自动选择最快、最稳定的链接</li>
                    <li>• <strong>手动指定模式</strong>：优先使用您指定的上传接口</li>
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">🖼️ 单张图片设置</h3>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900 mb-2">个性化配置</h4>
                  <p className="text-sm text-green-800 mb-2">
                    在上传历史中点击 <Badge variant="outline">跳转设置</Badge> 按钮
                  </p>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• <strong>自动最优</strong>：系统智能选择</li>
                    <li>• <strong>指定接口</strong>：优先使用特定接口</li>
                    <li>• <strong>优先级顺序</strong>：按接口优先级依次尝试</li>
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">🔗 链接格式说明</h3>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900">代理链接格式</h4>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {window.location.origin}/image/show/A2B3C4D5E6F7G8H9
                      </code>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>• 使用16位随机字符串，安全性高</p>
                      <p>• 永久有效，即使原始链接失效也能访问</p>
                      <p>• 自动故障转移，智能选择最佳链接</p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">⚡ 快速操作</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-3">
                    <h4 className="font-medium mb-2">上传完成后</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 立即获得永久代理链接</li>
                      <li>• 点击"复制链接"快速复制</li>
                      <li>• 点击外链图标预览图片</li>
                    </ul>
                  </div>
                  <div className="border rounded-lg p-3">
                    <h4 className="font-medium mb-2">历史记录中</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 点击"跳转设置"配置策略</li>
                      <li>• 复制永久代理链接</li>
                      <li>• 查看备份状态和进度</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
