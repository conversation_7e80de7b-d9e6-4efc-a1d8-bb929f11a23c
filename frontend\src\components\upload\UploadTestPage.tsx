import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { FileUpload } from './FileUpload';
import { EnhancedUploadHistory } from './EnhancedUploadHistory';
import { uploadStatus } from '../../lib/api';

export function UploadTestPage() {
  const [activeTab, setActiveTab] = useState<'upload' | 'history' | 'status'>('upload');
  const [providersOverview, setProvidersOverview] = useState<any>(null);
  const [loadingOverview, setLoadingOverview] = useState(false);

  const loadProvidersOverview = async () => {
    try {
      setLoadingOverview(true);
      const response = await uploadStatus.getProvidersOverview();
      if (response.success) {
        setProvidersOverview(response.data);
      }
    } catch (error) {
      console.error('加载接口概览失败:', error);
    } finally {
      setLoadingOverview(false);
    }
  };

  const handleUploadSuccess = () => {
    // 上传成功后可以刷新历史记录
    if (activeTab === 'history') {
      window.location.reload(); // 简单的刷新方式
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          多接口备份上传系统测试
        </h1>
        <p className="text-gray-600">
          测试一次上传，多接口静默备份功能
        </p>
      </div>

      {/* 标签页导航 */}
      <Card>
        <CardContent className="p-0">
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab('upload')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'upload'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              📤 文件上传
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              📋 上传历史
            </button>
            <button
              onClick={() => {
                setActiveTab('status');
                if (!providersOverview) {
                  loadProvidersOverview();
                }
              }}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'status'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
              }`}
            >
              📊 接口状态
            </button>
          </div>
        </CardContent>
      </Card>

      {/* 标签页内容 */}
      {activeTab === 'upload' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>多接口备份上传</CardTitle>
              <p className="text-sm text-gray-600">
                上传文件后，系统会立即上传到主接口（用户可见），同时在后台静默上传到所有其他接口作为备份。
              </p>
            </CardHeader>
            <CardContent>
              <FileUpload onUploadSuccess={handleUploadSuccess} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>功能说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-green-700">✅ 用户体验</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 只显示主接口上传状态</li>
                    <li>• 主接口完成后立即可用</li>
                    <li>• 获得永久有效的代理链接</li>
                    <li>• 相同文件自动去重</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-700">🔄 后台处理</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 静默上传到所有其他接口</li>
                    <li>• 自动故障转移</li>
                    <li>• 智能链接选择</li>
                    <li>• 备份进度跟踪</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'history' && (
        <EnhancedUploadHistory />
      )}

      {activeTab === 'status' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>接口状态概览</CardTitle>
                <Button
                  onClick={loadProvidersOverview}
                  disabled={loadingOverview}
                  size="sm"
                >
                  {loadingOverview ? '刷新中...' : '刷新状态'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loadingOverview ? (
                <div className="text-center py-8">
                  <div className="loading-spinner w-6 h-6 mx-auto mb-2" />
                  <p className="text-gray-600">加载中...</p>
                </div>
              ) : providersOverview ? (
                <div className="space-y-4">
                  {/* 概览统计 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-700">
                        {providersOverview.summary?.totalProviders || 0}
                      </div>
                      <div className="text-sm text-blue-600">总接口数</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-700">
                        {providersOverview.summary?.activeProviders || 0}
                      </div>
                      <div className="text-sm text-green-600">活跃接口</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-700">
                        {providersOverview.summary?.totalLinks || 0}
                      </div>
                      <div className="text-sm text-purple-600">总链接数</div>
                    </div>
                  </div>

                  {/* 接口详情 */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">接口详情</h4>
                    {providersOverview.providers?.map((provider: any) => (
                      <div key={provider.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <h5 className="font-medium">{provider.name}</h5>
                            <Badge variant="success">活跃</Badge>
                            {provider.priority === 'high' && (
                              <Badge variant="warning">高级</Badge>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {provider.totalLinks} 个链接
                          </div>
                        </div>
                        <div className="mt-2 text-sm text-gray-600">
                          <div>接口地址: {provider.endpoint}</div>
                          {provider.avgResponseTime && (
                            <div>平均响应时间: {provider.avgResponseTime}ms</div>
                          )}
                          <div>最大文件大小: {Math.round(provider.maxFileSize / 1024 / 1024)}MB</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">点击"刷新状态"加载接口信息</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
