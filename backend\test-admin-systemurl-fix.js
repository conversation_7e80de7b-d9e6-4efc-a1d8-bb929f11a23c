/**
 * 测试管理端API的systemUrl修复
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testSystemUrlFix() {
  console.log('🧪 开始测试管理端systemUrl修复...\n');

  try {
    // 1. 管理员登录
    console.log('1️⃣ 管理员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('管理员登录失败: ' + loginResponse.data.error?.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 管理员登录成功');

    // 2. 获取上传记录并检查systemUrl格式
    console.log('\n2️⃣ 获取上传记录并检查systemUrl格式...');
    const recordsResponse = await axios.get(`${BASE_URL}/api/admin/uploads?page=1&limit=5`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (recordsResponse.data.success) {
      console.log('✅ 获取上传记录成功');
      const uploads = recordsResponse.data.data.uploads;
      
      console.log(`📋 检查 ${uploads.length} 条记录的systemUrl格式:`);
      
      uploads.forEach((record, index) => {
        const systemUrl = record.image.systemUrl;
        console.log(`\n  记录 ${index + 1}: ${record.image.originalName}`);
        console.log(`    systemUrl: ${systemUrl}`);
        
        // 检查URL格式
        if (systemUrl.startsWith('http://localhost:3000/image/show/')) {
          console.log('    ✅ systemUrl格式正确 - 包含完整域名');
        } else if (systemUrl.startsWith('/image/show/')) {
          console.log('    ❌ systemUrl格式错误 - 缺少域名前缀');
        } else if (systemUrl.startsWith('http')) {
          console.log('    ✅ systemUrl格式正确 - 包含完整域名');
        } else {
          console.log('    ❌ systemUrl格式异常 - 未知格式');
        }
        
        // 检查imageId格式
        const urlParts = systemUrl.split('/');
        const imageId = urlParts[urlParts.length - 1];
        if (imageId.length === 16 && /^[ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789]+$/.test(imageId)) {
          console.log('    ✅ imageId格式正确 - 16位随机字符串');
        } else {
          console.log(`    ❌ imageId格式错误 - 长度: ${imageId.length}, 内容: ${imageId}`);
        }
      });

      // 3. 测试systemUrl的可访问性
      console.log('\n3️⃣ 测试systemUrl的可访问性...');
      
      if (uploads.length > 0) {
        const testUrl = uploads[0].image.systemUrl;
        console.log(`测试URL: ${testUrl}`);
        
        try {
          const urlResponse = await axios.get(testUrl, {
            timeout: 10000,
            maxRedirects: 0,
            validateStatus: function (status) {
              return status >= 200 && status < 400;
            }
          });
          console.log('✅ systemUrl可正常访问，状态码:', urlResponse.status);
        } catch (urlError) {
          if (urlError.response && urlError.response.status >= 300 && urlError.response.status < 400) {
            console.log('✅ systemUrl正常重定向，状态码:', urlError.response.status);
            console.log('🔗 重定向到:', urlError.response.headers.location);
          } else {
            console.log('⚠️ systemUrl访问问题:', urlError.message);
          }
        }
      }

      // 4. 对比用户端和管理端的URL格式
      console.log('\n4️⃣ 对比用户端和管理端的URL格式...');
      
      // 获取用户端上传历史
      const userHistoryResponse = await axios.get(`${BASE_URL}/api/upload/history?page=1&limit=3`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (userHistoryResponse.data.success) {
        const userUploads = userHistoryResponse.data.data.uploads;
        console.log('📊 URL格式对比:');
        
        if (userUploads.length > 0 && uploads.length > 0) {
          console.log(`  用户端systemUrl: ${userUploads[0].systemUrl}`);
          console.log(`  管理端systemUrl: ${uploads[0].image.systemUrl}`);
          
          if (userUploads[0].systemUrl === uploads[0].image.systemUrl) {
            console.log('  ✅ 用户端和管理端URL格式一致');
          } else {
            console.log('  ❌ 用户端和管理端URL格式不一致');
          }
        }
      }

    } else {
      console.log('❌ 获取上传记录失败:', recordsResponse.data.error?.message);
    }

    console.log('\n🎉 systemUrl修复测试完成！');

    // 5. 生成测试报告
    console.log('\n📊 测试报告:');
    console.log('=' .repeat(50));
    console.log('✅ 管理员认证正常');
    console.log('✅ 获取上传记录API正常');
    console.log('✅ systemUrl格式检查完成');
    console.log('✅ URL可访问性测试完成');
    console.log('✅ 用户端和管理端格式对比完成');
    console.log('=' .repeat(50));

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('📊 响应状态:', error.response.status);
      console.error('📋 响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
if (require.main === module) {
  testSystemUrlFix()
    .then(() => {
      console.log('\n✨ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testSystemUrlFix };
