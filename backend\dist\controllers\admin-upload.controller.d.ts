import { Request, Response, NextFunction } from 'express';
export declare class AdminUploadController {
    static getAllUploadRecords(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUploadStatistics(req: Request, res: Response, next: NextFunction): Promise<void>;
    static deleteUploadRecord(req: Request, res: Response, next: NextFunction): Promise<void>;
    static batchDeleteUploadRecords(req: Request, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=admin-upload.controller.d.ts.map