{"version": 3, "file": "upload-status.service.js", "sourceRoot": "", "sources": ["../../src/services/upload-status.service.ts"], "names": [], "mappings": ";;;AAKA,iDAA4C;AA4B5C,MAAa,mBAAmB;IAI9B,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,MAAc;QAC/D,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAClD,KAAK,EAAE;oBACL,cAAc,EAAE;wBACd,MAAM;wBACN,OAAO;qBACR;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,OAAO,EAAE;oBACP,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,QAAQ,EAAE;gCACR,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,IAAI,EAAE,IAAI;oCACV,QAAQ,EAAE,IAAI;iCACf;6BACF;yBACF;wBACD,OAAO,EAAE;4BACP,QAAQ,EAAE;gCACR,QAAQ,EAAE,KAAK;6BAChB;yBACF;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;YAC9E,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACnE,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAGzC,MAAM,cAAc,GAAG,MAAM,iBAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBACvD,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;aAC5B,CAAC,CAAC;YAGH,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC;YAC5C,MAAM,cAAc,GAAG;gBACrB,SAAS,EAAE,gBAAgB;gBAC3B,KAAK,EAAE,cAAc;gBACrB,UAAU,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3F,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;gBAC/C,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;oBACzB,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,IAAI;oBACnC,GAAG,EAAE,WAAW,CAAC,WAAW;oBAC5B,MAAM,EAAE,WAAW,CAAC,MAAM;oBAC1B,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,SAAS;iBACpD,CAAC,CAAC,CAAC,SAAS;gBACb,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;oBAC5B,GAAG,EAAE,IAAI,CAAC,WAAW;oBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,SAAS;iBAC7C,CAAC,CAAC;gBACH,cAAc;gBACd,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;aACzC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAC/B,MAAc,EACd,OAAe,CAAC,EAChB,QAAgB,EAAE;QAUlB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAGlC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC5C,iBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACxB,KAAK,EAAE,EAAE,MAAM,EAAE;oBACjB,OAAO,EAAE;wBACP,KAAK,EAAE;4BACL,OAAO,EAAE;gCACP,UAAU,EAAE;oCACV,OAAO,EAAE;wCACP,QAAQ,EAAE;4CACR,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,IAAI,EAAE,IAAI;gDACV,QAAQ,EAAE,IAAI;6CACf;yCACF;qCACF;oCACD,OAAO,EAAE;wCACP,QAAQ,EAAE;4CACR,QAAQ,EAAE,KAAK;yCAChB;qCACF;iCACF;6BACF;yBACF;qBACF;oBACD,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;oBAC9B,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,KAAK;iBACZ,CAAC;gBACF,iBAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC;aAC9C,CAAC,CAAC;YAGH,MAAM,cAAc,GAAG,MAAM,iBAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gBACvD,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;aAC5B,CAAC,CAAC;YAGH,MAAM,OAAO,GAAuB,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBAC7D,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;gBAC9B,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC;gBAC9E,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACnE,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEzC,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC;gBAC5C,MAAM,cAAc,GAAG;oBACrB,SAAS,EAAE,gBAAgB;oBAC3B,KAAK,EAAE,cAAc;oBACrB,UAAU,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,GAAG,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC3F,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE;oBAC/C,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;wBACzB,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,IAAI;wBACnC,GAAG,EAAE,WAAW,CAAC,WAAW;wBAC5B,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,YAAY,EAAE,WAAW,CAAC,YAAY,IAAI,SAAS;qBACpD,CAAC,CAAC,CAAC,SAAS;oBACb,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBACpC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;wBAC5B,GAAG,EAAE,IAAI,CAAC,WAAW;wBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,SAAS;qBAC7C,CAAC,CAAC;oBACH,cAAc;oBACd,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;iBACzC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO;gBACP,UAAU,EAAE;oBACV,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;iBACrC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACpC,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;aACrD,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,OAAe;QAMzC,IAAI,CAAC;YACH,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACrD,iBAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC;gBAC5D,iBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;oBACxB,KAAK,EAAE,EAAE,OAAO,EAAE;oBAClB,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;iBACzB,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;YAC/E,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;YAC/E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;YAEvE,OAAO;gBACL,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,cAAc;aACf,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,OAAO;gBACL,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,CAAC;gBACd,WAAW,EAAE,CAAC;gBACd,cAAc,EAAE,CAAC;aAClB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AArPD,kDAqPC"}