/**
 * 简化的上传测试 - 验证域名URL修复
 */

const axios = require('axios');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:3000';

// 创建测试图片
function createTestImage() {
  const pngBuffer = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
    0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE,
    0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54,
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01,
    0xE2, 0x21, 0xBC, 0x33, 0x00, 0x00, 0x00, 0x00,
    0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);
  return pngBuffer;
}

async function testSimpleUpload() {
  console.log('🧪 开始简化上传测试...\n');

  try {
    // 1. 使用默认管理员账户登录
    console.log('1️⃣ 使用管理员账户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('管理员登录失败: ' + loginResponse.data.error?.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 管理员登录成功');

    // 2. 测试上传功能
    console.log('\n2️⃣ 测试文件上传...');
    
    const testImageBuffer = createTestImage();
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'test-domain-fix.png',
      contentType: 'image/png'
    });

    const uploadResponse = await axios.post(`${BASE_URL}/api/upload/single`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${token}`
      },
      timeout: 30000,
    });

    if (!uploadResponse.data.success) {
      throw new Error('上传失败: ' + uploadResponse.data.error?.message);
    }

    console.log('✅ 文件上传成功！');
    console.log('\n📋 上传响应数据:');
    console.log(JSON.stringify(uploadResponse.data, null, 2));

    // 3. 验证响应格式
    console.log('\n3️⃣ 验证响应格式...');
    const { systemUrl, imageId } = uploadResponse.data.data;
    
    console.log(`systemUrl: ${systemUrl}`);
    console.log(`imageId: ${imageId}`);
    
    // 检查域名
    if (systemUrl.startsWith('http://localhost:3000/image/show/')) {
      console.log('✅ systemUrl包含完整域名');
    } else {
      console.log('❌ systemUrl缺少完整域名');
    }

    // 检查ID格式
    if (imageId.length === 16) {
      console.log('✅ imageId是16位字符串');
    } else {
      console.log(`❌ imageId长度不正确: ${imageId.length}`);
    }

    // 4. 测试代理链接访问
    console.log('\n4️⃣ 测试代理链接访问...');
    try {
      const proxyResponse = await axios.get(systemUrl, {
        timeout: 10000,
        maxRedirects: 0,
        validateStatus: function (status) {
          return status >= 200 && status < 400;
        }
      });
      console.log('✅ 代理链接访问成功，状态码:', proxyResponse.status);
    } catch (proxyError) {
      if (proxyError.response && proxyError.response.status >= 300 && proxyError.response.status < 400) {
        console.log('✅ 代理链接正常重定向，状态码:', proxyError.response.status);
        console.log('🔗 重定向到:', proxyError.response.headers.location);
      } else {
        console.log('⚠️ 代理链接访问问题:', proxyError.message);
      }
    }

    // 5. 测试上传历史
    console.log('\n5️⃣ 测试上传历史...');
    const historyResponse = await axios.get(`${BASE_URL}/api/upload/history?page=1&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (historyResponse.data.success) {
      console.log('✅ 获取上传历史成功');
      const uploads = historyResponse.data.data.uploads;
      
      if (uploads.length > 0) {
        console.log('\n📋 最新上传记录:');
        const latestUpload = uploads[0];
        console.log(`  文件名: ${latestUpload.originalName}`);
        console.log(`  systemUrl: ${latestUpload.systemUrl}`);
        console.log(`  链接数量: ${latestUpload.links.length}`);
        
        if (latestUpload.systemUrl.startsWith('http://localhost:3000/image/show/')) {
          console.log('  ✅ 历史记录中的systemUrl包含完整域名');
        } else {
          console.log('  ❌ 历史记录中的systemUrl缺少完整域名');
        }

        // 检查链接类型
        latestUpload.links.forEach((link, index) => {
          console.log(`  链接 ${index + 1}: ${link.provider} - ${link.url.substring(0, 50)}...`);
        });
      }
    } else {
      console.log('❌ 获取上传历史失败');
    }

    console.log('\n🎉 简化上传测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('📊 响应状态:', error.response.status);
      console.error('📋 响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
if (require.main === module) {
  testSimpleUpload()
    .then(() => {
      console.log('\n✨ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testSimpleUpload };
