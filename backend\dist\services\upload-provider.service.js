"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadProviderService = void 0;
const database_1 = require("../config/database");
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
class UploadProviderService {
    static async getAvailableProviders(userId, userLevel) {
        try {
            const levelVisibility = await database_1.prisma.levelProviderVisibility.findMany({
                where: {
                    level: userLevel,
                    isVisible: true,
                },
                include: {
                    provider: true
                },
                orderBy: {
                    displayOrder: 'asc'
                }
            });
            const userPermissions = await database_1.prisma.userProviderPermission.findMany({
                where: {
                    userId,
                    isActive: true,
                    OR: [
                        { expiresAt: null },
                        { expiresAt: { gt: new Date() } }
                    ]
                },
                include: {
                    provider: true
                }
            });
            const providers = new Map();
            levelVisibility.forEach(lv => {
                if (lv.provider && lv.provider.status === 'active') {
                    providers.set(lv.provider.id, {
                        id: lv.provider.id,
                        name: lv.provider.name,
                        endpoint: lv.provider.endpoint,
                        apiKey: lv.provider.apiKey || '',
                        config: lv.provider.config,
                        maxFileSize: Number(lv.provider.maxFileSize),
                        supportedFormats: lv.provider.supportedFormats,
                        isPremium: lv.provider.isPremium,
                    });
                }
            });
            userPermissions.forEach(up => {
                if (up.provider && up.provider.status === 'active') {
                    providers.set(up.provider.id, {
                        id: up.provider.id,
                        name: up.provider.name,
                        endpoint: up.provider.endpoint,
                        apiKey: up.provider.apiKey || '',
                        config: up.provider.config,
                        maxFileSize: Number(up.provider.maxFileSize),
                        supportedFormats: up.provider.supportedFormats,
                        isPremium: up.provider.isPremium,
                    });
                }
            });
            return Array.from(providers.values());
        }
        catch (error) {
            console.error('获取可用上传接口失败:', error);
            return [];
        }
    }
    static async uploadToProvider(provider, fileBuffer, fileName, mimeType) {
        const startTime = Date.now();
        try {
            let result;
            switch (provider.name.toLowerCase()) {
                case 'imgur':
                    result = await UploadProviderService.uploadToImgur(provider, fileBuffer, fileName);
                    break;
                case 'cloudinary':
                    result = await UploadProviderService.uploadToCloudinary(provider, fileBuffer, fileName);
                    break;
                case 'qiniu':
                case '七牛云':
                    result = await UploadProviderService.uploadToQiniu(provider, fileBuffer, fileName, mimeType);
                    break;
                case 'tcl':
                case 'tcl云':
                    result = await UploadProviderService.uploadToTCL(provider, fileBuffer, fileName, mimeType);
                    break;
                case '水滴云':
                case 'shuidi':
                case 'shuidi云':
                    result = await UploadProviderService.uploadToShuidi(provider, fileBuffer, fileName, mimeType);
                    break;
                case 'custom':
                    result = await UploadProviderService.uploadToCustomProvider(provider, fileBuffer, fileName, mimeType);
                    break;
                default:
                    result = await UploadProviderService.uploadToGenericProvider(provider, fileBuffer, fileName, mimeType);
                    break;
            }
            result.responseTime = Date.now() - startTime;
            return result;
        }
        catch (error) {
            console.error(`上传到 ${provider.name} 失败:`, error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '上传失败',
                responseTime: Date.now() - startTime,
                providerId: provider.id,
                providerName: provider.name,
            };
        }
    }
    static async uploadToImgur(provider, fileBuffer, fileName) {
        try {
            const formData = new form_data_1.default();
            formData.append('image', fileBuffer, fileName);
            const response = await axios_1.default.post('https://api.imgur.com/3/image', formData, {
                headers: {
                    'Authorization': `Client-ID ${provider.apiKey}`,
                    ...formData.getHeaders(),
                },
                timeout: 30000,
            });
            if (response.data.success) {
                return {
                    success: true,
                    url: response.data.data.link,
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
            else {
                return {
                    success: false,
                    error: 'Imgur API返回失败',
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
        }
        catch (error) {
            throw new Error(`Imgur上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async uploadToCloudinary(provider, fileBuffer, fileName) {
        try {
            const formData = new form_data_1.default();
            formData.append('file', fileBuffer, fileName);
            formData.append('upload_preset', provider.config?.uploadPreset || 'default');
            const cloudName = provider.config?.cloudName;
            const endpoint = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;
            const response = await axios_1.default.post(endpoint, formData, {
                headers: formData.getHeaders(),
                timeout: 30000,
            });
            return {
                success: true,
                url: response.data.secure_url,
                providerId: provider.id,
                providerName: provider.name,
            };
        }
        catch (error) {
            throw new Error(`Cloudinary上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async getProviderById(providerId) {
        try {
            const provider = await database_1.prisma.uploadProvider.findUnique({
                where: {
                    id: providerId,
                    status: 'active'
                }
            });
            if (!provider) {
                return null;
            }
            return {
                id: provider.id,
                name: provider.name,
                endpoint: provider.endpoint,
                apiKey: provider.apiKey || '',
                config: provider.config,
                maxFileSize: Number(provider.maxFileSize),
                supportedFormats: provider.supportedFormats,
                isPremium: provider.isPremium,
            };
        }
        catch (error) {
            console.error('获取接口配置失败:', error);
            return null;
        }
    }
    static async uploadToQiniu(provider, fileBuffer, fileName, mimeType) {
        try {
            const tokenEndpoint = provider.config?.tokenEndpoint || 'https://zgtdxh.kejie.org.cn/ajax/get-qiniu-token.php?prefix=user';
            const tokenResponse = await axios_1.default.get(tokenEndpoint, {
                timeout: 10000,
            });
            if (!tokenResponse.data || !tokenResponse.data.data || !tokenResponse.data.data.token) {
                throw new Error('获取七牛云token失败');
            }
            const token = tokenResponse.data.data.token;
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hour = String(now.getHours()).padStart(2, '0');
            const uniqueId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
            const ext = fileName.split('.').pop() || 'png';
            const fileKey = `${year}/${month}/${day}/${hour}/video/${uniqueId}.${ext}`;
            const formData = new form_data_1.default();
            formData.append('name', fileName);
            formData.append('chunk', '0');
            formData.append('chunks', '1');
            formData.append('key', fileKey);
            formData.append('token', token);
            formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });
            const uploadUrl = provider.endpoint || 'https://upload.qbox.me/';
            const response = await axios_1.default.post(uploadUrl, formData, {
                headers: formData.getHeaders(),
                timeout: 30000,
            });
            if (response.data && response.data.key) {
                const baseUrl = provider.config?.baseUrl || 'https://acad-upload.scimall.org.cn/';
                const finalUrl = baseUrl + response.data.key;
                return {
                    success: true,
                    url: finalUrl,
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
            else {
                return {
                    success: false,
                    error: '七牛云上传失败或响应格式错误',
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
        }
        catch (error) {
            throw new Error(`七牛云上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async uploadToTCL(provider, fileBuffer, fileName, mimeType) {
        try {
            const formData = new form_data_1.default();
            formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });
            const headers = {
                ...formData.getHeaders(),
            };
            if (provider.apiKey) {
                headers['Authorization'] = `Bearer ${provider.apiKey}`;
            }
            if (provider.config?.headers) {
                Object.assign(headers, provider.config.headers);
            }
            const response = await axios_1.default.post(provider.endpoint, formData, {
                headers,
                timeout: 30000,
            });
            const data = response.data?.data;
            if (data) {
                return {
                    success: true,
                    url: data,
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
            else {
                return {
                    success: false,
                    error: '无法从TCL接口响应中提取data字段',
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
        }
        catch (error) {
            throw new Error(`TCL接口上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async uploadToShuidi(provider, fileBuffer, fileName, mimeType) {
        try {
            const formData = new form_data_1.default();
            formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });
            const headers = {
                ...formData.getHeaders(),
            };
            if (provider.apiKey) {
                headers['Authorization'] = `Bearer ${provider.apiKey}`;
            }
            if (provider.config?.headers) {
                Object.assign(headers, provider.config.headers);
            }
            const response = await axios_1.default.post(provider.endpoint, formData, {
                headers,
                timeout: 30000,
            });
            if (response.data && response.data.path) {
                const baseUrl = provider.config?.baseUrl || 'https://filehuoshan.shuidi.cn/img/';
                const suffix = provider.config?.suffix || '/0x0.jpg';
                const fullUrl = `${baseUrl}${response.data.path}${suffix}`;
                return {
                    success: true,
                    url: fullUrl,
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
            else {
                return {
                    success: false,
                    error: '无法从水滴云接口响应中提取path字段',
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
        }
        catch (error) {
            throw new Error(`水滴云接口上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async uploadToCustomProvider(provider, fileBuffer, fileName, mimeType) {
        try {
            const formData = new form_data_1.default();
            formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });
            if (provider.config) {
                Object.keys(provider.config).forEach(key => {
                    if (key !== 'headers') {
                        formData.append(key, provider.config[key]);
                    }
                });
            }
            const headers = {
                ...formData.getHeaders(),
            };
            if (provider.apiKey) {
                headers['Authorization'] = `Bearer ${provider.apiKey}`;
            }
            if (provider.config?.headers) {
                Object.assign(headers, provider.config.headers);
            }
            const response = await axios_1.default.post(provider.endpoint, formData, {
                headers,
                timeout: 30000,
            });
            const urlPath = provider.config?.responseUrlPath || 'url';
            const url = UploadProviderService.getNestedProperty(response.data, urlPath);
            if (url) {
                return {
                    success: true,
                    url,
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
            else {
                return {
                    success: false,
                    error: '无法从响应中提取URL',
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
        }
        catch (error) {
            throw new Error(`自定义接口上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async uploadToGenericProvider(provider, fileBuffer, fileName, mimeType) {
        try {
            const formData = new form_data_1.default();
            formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });
            const headers = {
                ...formData.getHeaders(),
            };
            if (provider.apiKey) {
                headers['Authorization'] = `Bearer ${provider.apiKey}`;
            }
            const response = await axios_1.default.post(provider.endpoint, formData, {
                headers,
                timeout: 30000,
            });
            const possibleUrlFields = ['url', 'link', 'src', 'image_url', 'file_url', 'data.url', 'result.url'];
            let url;
            for (const field of possibleUrlFields) {
                url = UploadProviderService.getNestedProperty(response.data, field);
                if (url)
                    break;
            }
            if (url) {
                return {
                    success: true,
                    url,
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
            else {
                return {
                    success: false,
                    error: '无法从响应中提取URL',
                    providerId: provider.id,
                    providerName: provider.name,
                };
            }
        }
        catch (error) {
            throw new Error(`通用接口上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    static async uploadToMultipleProviders(providers, fileBuffer, fileName, mimeType) {
        const uploadPromises = providers.map(provider => UploadProviderService.uploadToProvider(provider, fileBuffer, fileName, mimeType));
        try {
            return await Promise.all(uploadPromises);
        }
        catch (error) {
            console.error('批量上传失败:', error);
            return [];
        }
    }
    static async saveUploadResults(imageId, results) {
        try {
            const linkData = results
                .filter(result => result.success && result.url)
                .map(result => ({
                imageId,
                providerId: result.providerId,
                externalUrl: result.url,
                status: 'active',
                responseTime: result.responseTime ?? null,
                lastChecked: new Date(),
            }));
            if (linkData.length > 0) {
                await database_1.prisma.imageLink.createMany({
                    data: linkData,
                });
            }
            const hasSuccessfulUpload = results.some(result => result.success);
            await database_1.prisma.image.update({
                where: { id: imageId },
                data: {
                    uploadStatus: hasSuccessfulUpload ? 'completed' : 'failed',
                }
            });
        }
        catch (error) {
            console.error('保存上传结果失败:', error);
        }
    }
    static async checkProviderHealth(provider) {
        try {
            const testImageBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
            const result = await UploadProviderService.uploadToProvider(provider, testImageBuffer, 'test.png', 'image/png');
            return result.success;
        }
        catch {
            return false;
        }
    }
    static getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    static validateProviderConfig(provider) {
        const errors = [];
        if (!provider.name) {
            errors.push('接口名称不能为空');
        }
        if (!provider.endpoint) {
            errors.push('接口地址不能为空');
        }
        if (provider.maxFileSize <= 0) {
            errors.push('最大文件大小必须大于0');
        }
        if (!provider.supportedFormats || provider.supportedFormats.length === 0) {
            errors.push('必须指定支持的文件格式');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
}
exports.UploadProviderService = UploadProviderService;
//# sourceMappingURL=upload-provider.service.js.map