"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkSelectionService = void 0;
const database_1 = require("../config/database");
class LinkSelectionService {
    static async selectBestLink(imageId, userId) {
        try {
            let userConfig = null;
            if (userId) {
                userConfig = await database_1.prisma.userImageRedirectConfig.findUnique({
                    where: {
                        userId_imageId: {
                            userId,
                            imageId
                        }
                    },
                    include: {
                        preferredProvider: true
                    }
                });
            }
            const image = await database_1.prisma.image.findUnique({
                where: { id: imageId },
                include: {
                    imageLinks: {
                        include: {
                            provider: true
                        },
                        where: {
                            status: 'active'
                        }
                    }
                }
            });
            if (!image || !image.imageLinks || image.imageLinks.length === 0) {
                return null;
            }
            if (userConfig && userConfig.isEnabled) {
                return await LinkSelectionService.selectByUserStrategy(image.imageLinks, userConfig, userId);
            }
            const sortedByCreation = image.imageLinks.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
            if (sortedByCreation.length > 0 && sortedByCreation[0] && sortedByCreation[0].provider) {
                console.log(`🎯 没有用户配置，选择最早创建的链接: ${sortedByCreation[0].provider.name} (ID: ${sortedByCreation[0].providerId})`);
                return LinkSelectionService.formatSelectedLink(sortedByCreation[0]);
            }
            return await LinkSelectionService.selectByAutoStrategy(image.imageLinks, userId);
        }
        catch (error) {
            console.error('链接选择服务错误:', error);
            return null;
        }
    }
    static async selectByUserStrategy(links, userConfig, userId) {
        switch (userConfig.redirectStrategy) {
            case 'provider':
                if (userConfig.preferredProviderId) {
                    const preferredLink = links.find(link => link.providerId === userConfig.preferredProviderId);
                    if (preferredLink) {
                        return LinkSelectionService.formatSelectedLink(preferredLink);
                    }
                }
                if (userConfig.fallbackStrategy === 'auto') {
                    return await LinkSelectionService.selectByAutoStrategy(links, userId);
                }
                break;
            case 'priority':
                return await LinkSelectionService.selectByPriorityStrategy(links, userId);
            case 'auto':
            default:
                return await LinkSelectionService.selectByAutoStrategy(links, userId);
        }
        return null;
    }
    static async selectByAutoStrategy(links, userId) {
        if (links.length === 0)
            return null;
        if (links.length === 1)
            return LinkSelectionService.formatSelectedLink(links[0]);
        let userLevel = 'free';
        if (userId) {
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { userLevel: true }
            });
            userLevel = user?.userLevel || 'free';
        }
        const accessibleLinks = await LinkSelectionService.filterAccessibleLinks(links, userLevel);
        if (accessibleLinks.length === 0) {
            return LinkSelectionService.formatSelectedLink(links[0]);
        }
        const linkScores = await LinkSelectionService.calculateLinkScores(accessibleLinks);
        const bestLink = linkScores.reduce((best, current) => current.score > best.score ? current : best);
        const selectedLink = accessibleLinks.find(link => link.id === bestLink.linkId);
        return selectedLink ? LinkSelectionService.formatSelectedLink(selectedLink) : null;
    }
    static async selectByPriorityStrategy(links, userId) {
        const sortedLinks = links.sort((a, b) => {
            return (a.provider.priority || 999) - (b.provider.priority || 999);
        });
        return LinkSelectionService.formatSelectedLink(sortedLinks[0]);
    }
    static async filterAccessibleLinks(links, userLevel) {
        const accessibleLinks = [];
        for (const link of links) {
            const levelVisibility = await database_1.prisma.levelProviderVisibility.findUnique({
                where: {
                    level_providerId: {
                        level: userLevel,
                        providerId: link.providerId
                    }
                }
            });
            if (levelVisibility?.isVisible) {
                accessibleLinks.push(link);
            }
        }
        return accessibleLinks;
    }
    static async calculateLinkScores(links) {
        const scores = [];
        for (const link of links) {
            let score = 100;
            let reasons = [];
            if (link.responseTime) {
                const responseScore = Math.max(0, 50 - (link.responseTime / 100));
                score += responseScore;
                reasons.push(`响应时间: ${link.responseTime}ms (+${responseScore.toFixed(1)})`);
            }
            const errorPenalty = link.errorCount * 10;
            score -= errorPenalty;
            if (errorPenalty > 0) {
                reasons.push(`错误次数: ${link.errorCount} (-${errorPenalty})`);
            }
            const priorityScore = Math.max(0, 20 - (link.provider.priority || 10));
            score += priorityScore;
            reasons.push(`优先级: ${link.provider.priority || 10} (+${priorityScore})`);
            if (link.lastChecked) {
                const hoursSinceCheck = (Date.now() - new Date(link.lastChecked).getTime()) / (1000 * 60 * 60);
                const freshnessScore = Math.max(0, 10 - hoursSinceCheck);
                score += freshnessScore;
                reasons.push(`检查时效: ${hoursSinceCheck.toFixed(1)}h前 (+${freshnessScore.toFixed(1)})`);
            }
            scores.push({
                linkId: link.id,
                score: Math.max(0, score),
                reason: reasons.join(', ')
            });
        }
        return scores;
    }
    static formatSelectedLink(link) {
        return {
            id: link.id,
            externalUrl: link.externalUrl,
            providerId: link.providerId,
            providerName: link.provider.name,
            responseTime: link.responseTime,
            status: link.status
        };
    }
    static async getLinkHealthStats(imageId) {
        const links = await database_1.prisma.imageLink.findMany({
            where: { imageId },
            include: {
                provider: {
                    select: {
                        id: true,
                        name: true,
                        status: true
                    }
                }
            }
        });
        const stats = {
            total: links.length,
            active: links.filter(l => l.status === 'active').length,
            failed: links.filter(l => l.status === 'failed').length,
            checking: links.filter(l => l.status === 'checking').length,
            avgResponseTime: 0,
            providers: links.map(l => ({
                id: l.provider.id,
                name: l.provider.name,
                status: l.status,
                responseTime: l.responseTime,
                errorCount: l.errorCount
            }))
        };
        const responseTimes = links
            .filter(l => l.responseTime !== null)
            .map(l => l.responseTime);
        if (responseTimes.length > 0) {
            stats.avgResponseTime = Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length);
        }
        return stats;
    }
}
exports.LinkSelectionService = LinkSelectionService;
//# sourceMappingURL=link-selection.service.js.map