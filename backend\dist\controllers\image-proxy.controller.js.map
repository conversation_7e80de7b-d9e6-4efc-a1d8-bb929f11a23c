{"version": 3, "file": "image-proxy.controller.js", "sourceRoot": "", "sources": ["../../src/controllers/image-proxy.controller.ts"], "names": [], "mappings": ";;;AACA,iDAA4C;AAC5C,+EAA0E;AAC1E,mEAA8D;AAC9D,oCAAsC;AAGtC,MAAa,oBAAoB;IAI/B,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAC1E,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAC/B,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;YAClD,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS,CAAC;YAGrE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,kBAAU,CAAC,aAAa;wBAC9B,OAAO,EAAE,SAAS;wBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACa,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAGD,IAAI,UAAkB,CAAC;YACvB,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAE1B,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBAEN,MAAM,UAAU,GAAG,MAAM,iCAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC/D,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE;4BACL,IAAI,EAAE,kBAAU,CAAC,SAAS;4BAC1B,OAAO,EAAE,OAAO;4BAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACa,CAAC,CAAC;oBAClB,OAAO;gBACT,CAAC;gBACD,UAAU,GAAG,UAAU,CAAC;YAC1B,CAAC;YAGD,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,OAAO,EAAE;oBACP,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,QAAQ,EAAE,IAAI;yBACf;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,kBAAU,CAAC,SAAS;wBAC1B,OAAO,EAAE,OAAO;wBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACa,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAGD,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,kBAAU,CAAC,SAAS;wBAC1B,OAAO,EAAE,UAAU;wBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACa,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,6CAAoB,CAAC,cAAc,CAC5D,UAAU,EACV,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CACtC,CAAC;YAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,kBAAU,CAAC,mBAAmB;wBACpC,OAAO,EAAE,WAAW;wBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACa,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAGD,MAAM,oBAAoB,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YAGxG,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;QAE9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACvE,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAE/B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,kBAAU,CAAC,aAAa;wBAC9B,OAAO,EAAE,SAAS;wBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACa,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAGD,IAAI,UAAkB,CAAC;YACvB,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAE1B,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBAEN,MAAM,UAAU,GAAG,MAAM,iCAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC/D,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE;4BACL,IAAI,EAAE,kBAAU,CAAC,SAAS;4BAC1B,OAAO,EAAE,OAAO;4BAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;yBACpC;qBACa,CAAC,CAAC;oBAClB,OAAO;gBACT,CAAC;gBACD,UAAU,GAAG,UAAU,CAAC;YAC1B,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,OAAO,EAAE;oBACP,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,QAAQ,EAAE;gCACR,MAAM,EAAE;oCACN,EAAE,EAAE,IAAI;oCACR,IAAI,EAAE,IAAI;oCACV,WAAW,EAAE,IAAI;oCACjB,MAAM,EAAE,IAAI;oCACZ,SAAS,EAAE,IAAI;iCAChB;6BACF;yBACF;wBACD,OAAO,EAAE;4BACP,EAAE,MAAM,EAAE,KAAK,EAAE;4BACjB,EAAE,YAAY,EAAE,KAAK,EAAE;4BACvB,EAAE,UAAU,EAAE,KAAK,EAAE;yBACtB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,kBAAU,CAAC,SAAS;wBAC1B,OAAO,EAAE,OAAO;wBAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACa,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAED,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBACnC,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,cAAc,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAC5C,EAAE,EAAE,IAAI,CAAC,EAAE;wBACX,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,YAAY,EAAE,IAAI,CAAC,YAAY;wBAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;wBAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;qBACxB,CAAC,CAAC;iBACJ;aACa,CAAC,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,SAAS,CAC5B,OAAe,EACf,SAAiB,EACjB,SAAkB,EAClB,OAAgB,EAChB,UAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,iBAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5B,IAAI,EAAE;oBACJ,OAAO;oBACP,SAAS;oBACT,SAAS,EAAE,SAAS,IAAI,IAAI;oBAC5B,OAAO,EAAE,OAAO,IAAI,IAAI;oBACxB,YAAY,EAAE,UAAU,IAAI,IAAI;iBACjC;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAEpC,CAAC;IACH,CAAC;CACF;AApPD,oDAoPC"}