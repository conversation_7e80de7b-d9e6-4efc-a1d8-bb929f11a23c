/**
 * 第三方接口工厂类
 * 负责创建和管理各种第三方接口适配器
 */

import { BaseProvider, ProviderConfig } from './base.provider';
import { QiniuProvider } from './qiniu.provider';
import { ImgurProvider } from './imgur.provider';

export class ProviderFactory {
  /**
   * 创建接口适配器实例
   */
  static createProvider(config: ProviderConfig): BaseProvider {
    const providerName = config.name.toLowerCase();

    switch (providerName) {
      case 'qiniu':
      case '七牛云':
        return new QiniuProvider(config);
      
      case 'imgur':
        return new ImgurProvider(config);
      
      // 可以继续添加更多接口
      // case 'cloudinary':
      //   return new CloudinaryProvider(config);
      // 
      // case 'aws-s3':
      //   return new AwsS3Provider(config);
      
      default:
        throw new Error(`不支持的接口类型: ${config.name}`);
    }
  }

  /**
   * 获取支持的接口类型列表
   */
  static getSupportedProviders(): string[] {
    return [
      'qiniu',
      '七牛云',
      'imgur',
      // 'cloudinary',
      // 'aws-s3',
    ];
  }

  /**
   * 检查接口类型是否支持
   */
  static isProviderSupported(providerName: string): boolean {
    return this.getSupportedProviders().includes(providerName.toLowerCase());
  }

  /**
   * 批量创建接口适配器
   */
  static createProviders(configs: ProviderConfig[]): BaseProvider[] {
    return configs.map(config => this.createProvider(config));
  }

  /**
   * 创建接口适配器并进行健康检查
   */
  static async createProviderWithHealthCheck(config: ProviderConfig): Promise<{
    provider: BaseProvider;
    isHealthy: boolean;
    error?: string;
  }> {
    try {
      const provider = this.createProvider(config);
      const healthResult = await provider.healthCheck();
      
      return {
        provider,
        isHealthy: healthResult.isHealthy,
        ...(healthResult.error && { error: healthResult.error }),
      };
    } catch (error) {
      return {
        provider: this.createProvider(config),
        isHealthy: false,
        error: error instanceof Error ? error.message : '创建接口适配器失败',
      };
    }
  }
}
