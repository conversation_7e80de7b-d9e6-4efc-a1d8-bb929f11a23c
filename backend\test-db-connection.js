const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testConnection() {
  try {
    console.log('测试数据库连接...');
    
    // 简单的连接测试
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ 数据库连接成功');
    
    // 检查表是否存在
    const count = await prisma.uploadProvider.count();
    console.log(`✅ upload_providers表存在，当前记录数: ${count}`);
    
    // 列出所有接口
    const providers = await prisma.uploadProvider.findMany({
      select: { id: true, name: true, status: true, priority: true }
    });
    
    console.log('📋 当前接口列表:');
    providers.forEach(p => {
      console.log(`  ${p.id}: ${p.name} (${p.status}, 优先级: ${p.priority})`);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
