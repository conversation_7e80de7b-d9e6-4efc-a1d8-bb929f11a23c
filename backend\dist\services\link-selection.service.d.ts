export interface SelectedLink {
    id: number;
    externalUrl: string;
    providerId: number;
    providerName: string;
    responseTime?: number;
    status: string;
}
export interface LinkScore {
    linkId: number;
    score: number;
    reason: string;
}
export declare class LinkSelectionService {
    static selectBestLink(imageId: number, userId?: number): Promise<SelectedLink | null>;
    private static selectByUserStrategy;
    private static selectByAutoStrategy;
    private static selectByPriorityStrategy;
    private static filterAccessibleLinks;
    private static calculateLinkScores;
    private static formatSelectedLink;
    static getLinkHealthStats(imageId: number): Promise<any>;
}
//# sourceMappingURL=link-selection.service.d.ts.map