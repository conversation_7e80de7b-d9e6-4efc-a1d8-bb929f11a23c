import { Router } from 'express';
import { ImageRedirectConfigController } from '../controllers/image-redirect-config.controller';
import { authenticateToken, requestId, recordIP } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { body, param } from 'express-validator';

const router = Router();

// 应用中间件
router.use(requestId);
router.use(recordIP);
router.use(authenticateToken); // 所有配置接口都需要认证

/**
 * @route   GET /api/image-redirect-config/providers
 * @desc    获取用户可用的提供商列表
 * @access  Private
 */
router.get('/providers', ImageRedirectConfigController.getUserAvailableProviders);

/**
 * @route   GET /api/image-redirect-config/global
 * @desc    获取用户全局跳转配置
 * @access  Private
 */
router.get('/global', ImageRedirectConfigController.getUserGlobalConfig);

/**
 * @route   POST /api/image-redirect-config/global
 * @desc    保存用户全局跳转配置
 * @access  Private
 */
router.post('/global',
  body('redirectMode')
    .isIn(['auto', 'manual'])
    .withMessage('跳转模式必须是 auto 或 manual'),
  body('preferredProviderId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('提供商ID必须是正整数'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('启用状态必须是布尔值'),
  validateRequest,
  ImageRedirectConfigController.saveUserGlobalConfig
);

/**
 * @route   GET /api/image-redirect-config/:imageId
 * @desc    获取用户图片的跳转配置
 * @access  Private
 */
router.get('/:imageId',
  param('imageId').isInt({ min: 1 }).withMessage('图片ID必须是正整数'),
  validateRequest,
  ImageRedirectConfigController.getUserImageConfig
);

/**
 * @route   PUT /api/image-redirect-config/:imageId
 * @desc    设置用户图片的跳转配置
 * @access  Private
 */
router.put('/:imageId',
  param('imageId').isInt({ min: 1 }).withMessage('图片ID必须是正整数'),
  body('redirectStrategy')
    .optional()
    .isIn(['auto', 'provider', 'priority'])
    .withMessage('跳转策略必须是 auto、provider 或 priority'),
  body('preferredProviderId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('提供商ID必须是正整数'),
  body('fallbackStrategy')
    .optional()
    .isIn(['auto', 'priority'])
    .withMessage('备用策略必须是 auto 或 priority'),
  body('isEnabled')
    .optional()
    .isBoolean()
    .withMessage('启用状态必须是布尔值'),
  validateRequest,
  ImageRedirectConfigController.setUserImageConfig
);

/**
 * @route   POST /api/image-redirect-config/batch
 * @desc    批量获取用户图片的跳转配置
 * @access  Private
 */
router.post('/batch',
  body('imageIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('图片ID数组长度必须在1-100之间'),
  body('imageIds.*')
    .isInt({ min: 1 })
    .withMessage('每个图片ID必须是正整数'),
  validateRequest,
  ImageRedirectConfigController.getBatchUserImageConfigs
);

export default router;
