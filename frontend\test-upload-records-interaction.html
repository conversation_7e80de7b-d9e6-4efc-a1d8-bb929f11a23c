<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传记录交互测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .record-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .record-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        .record-actions {
            display: flex;
            gap: 5px;
        }
        .btn-view {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .detail-item {
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .detail-label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
        .detail-value {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 上传记录交互测试</h1>
        <p>测试管理端上传记录的查看功能</p>

        <!-- 登录测试 -->
        <div class="test-section">
            <h3>1. 管理员登录</h3>
            <button onclick="testLogin()">登录</button>
            <div id="loginResult" class="result"></div>
        </div>

        <!-- 获取上传记录 -->
        <div class="test-section">
            <h3>2. 获取上传记录</h3>
            <button onclick="loadUploadRecords()">加载记录</button>
            <div id="recordsResult" class="result"></div>
        </div>

        <!-- 模拟记录列表 -->}
        <div class="test-section">
            <h3>3. 模拟记录列表（测试查看功能）</h3>
            <div id="recordsList"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <h2>上传记录详情</h2>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        let uploadRecords = [];

        // 登录测试
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin123'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 管理员登录成功！\nToken: ${authToken.substring(0, 20)}...`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败: ${data.error?.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 加载上传记录
        async function loadUploadRecords() {
            const resultDiv = document.getElementById('recordsResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先登录';
                return;
            }

            try {
                const response = await fetch('/api/admin/uploads?page=1&limit=5', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    uploadRecords = data.data.uploads;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取上传记录成功！\n共 ${uploadRecords.length} 条记录`;
                    
                    // 渲染记录列表
                    renderRecordsList();
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取记录失败: ${data.error?.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 渲染记录列表
        function renderRecordsList() {
            const listDiv = document.getElementById('recordsList');
            
            if (uploadRecords.length === 0) {
                listDiv.innerHTML = '<p>暂无上传记录</p>';
                return;
            }

            let html = '';
            uploadRecords.forEach((record, index) => {
                html += `
                    <div class="record-item">
                        <div class="record-header">
                            <div>
                                <strong>${record.image.originalName}</strong>
                                <span style="margin-left: 10px; font-size: 12px; color: #666;">
                                    ${formatFileSize(record.image.fileSize)} | ${record.image.mimeType}
                                </span>
                            </div>
                            <div class="record-actions">
                                <button class="btn-view" onclick="viewRecord(${index})">
                                    👁️ 查看
                                </button>
                                <button class="btn-delete" onclick="deleteRecord(${record.id})">
                                    🗑️ 删除
                                </button>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            用户: ${record.user.username} (${record.user.email}) | 
                            等级: ${record.user.userLevel} | 
                            上传时间: ${new Date(record.createdAt).toLocaleString('zh-CN')} |
                            访问次数: ${record.accessCount}
                        </div>
                    </div>
                `;
            });
            
            listDiv.innerHTML = html;
        }

        // 查看记录详情
        function viewRecord(index) {
            const record = uploadRecords[index];
            if (!record) return;

            const modalContent = document.getElementById('modalContent');
            
            modalContent.innerHTML = `
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">文件名</div>
                        <div class="detail-value">${record.image.originalName}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">文件大小</div>
                        <div class="detail-value">${formatFileSize(record.image.fileSize)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">文件类型</div>
                        <div class="detail-value">${record.image.mimeType}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">文件状态</div>
                        <div class="detail-value">${record.image.isDeleted ? '已删除' : record.image.uploadStatus}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">用户名</div>
                        <div class="detail-value">${record.user.username}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">用户邮箱</div>
                        <div class="detail-value">${record.user.email}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">用户等级</div>
                        <div class="detail-value">${record.user.userLevel}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">上传时间</div>
                        <div class="detail-value">${new Date(record.createdAt).toLocaleString('zh-CN')}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">访问次数</div>
                        <div class="detail-value">${record.accessCount}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">原始上传者</div>
                        <div class="detail-value">${record.isOriginalUploader ? '是' : '否'}</div>
                    </div>
                </div>
                
                ${record.image.width && record.image.height ? `
                    <div class="detail-item" style="margin-bottom: 15px;">
                        <div class="detail-label">图片尺寸</div>
                        <div class="detail-value">${record.image.width} × ${record.image.height}</div>
                    </div>
                ` : ''}
                
                <div class="detail-item" style="margin-bottom: 15px;">
                    <div class="detail-label">系统URL</div>
                    <div class="detail-value">
                        <a href="${record.image.systemUrl}" target="_blank" style="color: #007bff; word-break: break-all;">
                            ${record.image.systemUrl}
                        </a>
                    </div>
                </div>
                
                ${record.image.links.length > 0 ? `
                    <div style="margin-top: 20px;">
                        <h4>存储链接</h4>
                        ${record.image.links.map(link => `
                            <div class="detail-item" style="margin-bottom: 10px;">
                                <div class="detail-label">${link.provider} (${link.status})</div>
                                <div class="detail-value">
                                    <a href="${link.url}" target="_blank" style="color: #007bff; word-break: break-all; font-size: 12px;">
                                        ${link.url}
                                    </a>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                ` : ''}
                
                <div style="margin-top: 20px; text-align: right;">
                    <button onclick="closeModal()" style="margin-right: 10px;">关闭</button>
                    <button onclick="deleteRecord(${record.id}); closeModal();" style="background: #dc3545;">删除记录</button>
                </div>
            `;

            document.getElementById('detailModal').classList.add('show');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('detailModal').classList.remove('show');
        }

        // 删除记录
        async function deleteRecord(recordId) {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            if (!confirm('确定要删除这条记录吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/uploads/${recordId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ reason: '管理员删除' })
                });

                const data = await response.json();
                
                if (data.success) {
                    alert('删除成功！');
                    loadUploadRecords(); // 重新加载列表
                } else {
                    alert(`删除失败: ${data.error?.message}`);
                }
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            const size = parseInt(bytes);
            if (size < 1024) return `${size} B`;
            if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
            if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
            return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // 点击模态框背景关闭
        document.getElementById('detailModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeModal();
            }
        });

        // 页面加载时的提示
        window.onload = function() {
            console.log('📋 上传记录交互测试页面已加载');
            console.log('💡 请按顺序执行：登录 → 加载记录 → 点击查看按钮测试');
        };
    </script>
</body>
</html>
