/**
 * 测试链接选择逻辑修复
 */

const { PrismaClient } = require('@prisma/client');
const { LinkSelectionService } = require('./dist/services/link-selection.service');

const prisma = new PrismaClient();

async function testLinkSelection() {
  try {
    console.log('🧪 测试链接选择逻辑修复\n');

    // 1. 查找最近上传的图片
    const recentImage = await prisma.image.findFirst({
      include: {
        imageLinks: {
          include: {
            provider: { select: { id: true, name: true, priority: true } }
          },
          orderBy: { createdAt: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (!recentImage) {
      console.log('❌ 没有找到测试图片');
      return;
    }

    console.log(`📷 测试图片: ${recentImage.originalName} (ID: ${recentImage.id})`);
    console.log(`🔗 系统链接: ${recentImage.systemUrl}`);
    console.log(`📊 链接数量: ${recentImage.imageLinks.length}\n`);

    // 2. 显示所有链接信息
    console.log('🔗 所有可用链接:');
    recentImage.imageLinks.forEach((link, index) => {
      const createdTime = new Date(link.createdAt).toLocaleString();
      console.log(`  ${index + 1}. ${link.provider.name} (ID: ${link.providerId})`);
      console.log(`     优先级: ${link.provider.priority}`);
      console.log(`     状态: ${link.status}`);
      console.log(`     创建时间: ${createdTime}`);
      console.log(`     URL: ${link.externalUrl.substring(0, 50)}...`);
      console.log('');
    });

    // 3. 测试链接选择（无用户配置）
    console.log('🎯 测试链接选择（无用户配置）:');
    const selectedLink = await LinkSelectionService.selectBestLink(recentImage.id);
    
    if (selectedLink) {
      console.log(`✅ 选择的链接: ${selectedLink.providerName} (ID: ${selectedLink.providerId})`);
      console.log(`🔗 跳转URL: ${selectedLink.externalUrl}`);
      
      // 检查是否选择了最早创建的链接
      const earliestLink = recentImage.imageLinks.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )[0];
      
      if (selectedLink.providerId === earliestLink.providerId) {
        console.log('✅ 正确选择了最早创建的链接（用户上传时选择的主接口）');
      } else {
        console.log('⚠️  没有选择最早创建的链接');
        console.log(`   期望: ${earliestLink.provider.name} (ID: ${earliestLink.providerId})`);
        console.log(`   实际: ${selectedLink.providerName} (ID: ${selectedLink.providerId})`);
      }
    } else {
      console.log('❌ 没有选择到任何链接');
    }

    // 4. 测试用户配置的链接选择
    console.log('\n🎯 测试用户配置的链接选择:');
    
    // 查找有配置的用户
    const userWithConfig = await prisma.userImageRedirectConfig.findFirst({
      where: { imageId: recentImage.id },
      include: {
        preferredProvider: { select: { id: true, name: true } }
      }
    });

    if (userWithConfig) {
      console.log(`👤 找到用户配置 (用户ID: ${userWithConfig.userId})`);
      console.log(`⚙️  策略: ${userWithConfig.redirectStrategy}`);
      console.log(`🎯 优先接口: ${userWithConfig.preferredProvider?.name || '无'}`);
      
      const userSelectedLink = await LinkSelectionService.selectBestLink(
        recentImage.id, 
        userWithConfig.userId
      );
      
      if (userSelectedLink) {
        console.log(`✅ 用户配置选择: ${userSelectedLink.providerName} (ID: ${userSelectedLink.providerId})`);
      }
    } else {
      console.log('ℹ️  该图片没有用户配置');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLinkSelection();
