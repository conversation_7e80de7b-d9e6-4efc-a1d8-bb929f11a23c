"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageProxyController = void 0;
const database_1 = require("../config/database");
const link_selection_service_1 = require("../services/link-selection.service");
const image_id_service_1 = require("../services/image-id.service");
const types_1 = require("../types");
class ImageProxyController {
    static async redirectToImage(req, res, next) {
        try {
            const { imageId } = req.params;
            const userId = req.headers['x-user-id'];
            const userAgent = req.get('User-Agent');
            const referer = req.get('Referer');
            const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
            if (!imageId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的图片ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            let imageIdInt;
            if (/^\d+$/.test(imageId)) {
                imageIdInt = parseInt(imageId);
            }
            else {
                const internalId = await image_id_service_1.ImageIdService.getInternalId(imageId);
                if (!internalId) {
                    res.status(404).json({
                        success: false,
                        error: {
                            code: types_1.ErrorCodes.NOT_FOUND,
                            message: '图片不存在',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
                imageIdInt = internalId;
            }
            const image = await database_1.prisma.image.findUnique({
                where: { id: imageIdInt },
                include: {
                    imageLinks: {
                        include: {
                            provider: true
                        }
                    }
                }
            });
            if (!image) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '图片不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (!image.imageLinks || image.imageLinks.length === 0) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '图片暂无可用链接',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const selectedLink = await link_selection_service_1.LinkSelectionService.selectBestLink(imageIdInt, userId ? parseInt(userId) : undefined);
            if (!selectedLink) {
                res.status(503).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.SERVICE_UNAVAILABLE,
                        message: '暂无可用的图片链接',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            await ImageProxyController.logAccess(imageIdInt, clientIp, userAgent, referer, selectedLink.providerId);
            res.redirect(302, selectedLink.externalUrl);
        }
        catch (error) {
            console.error('图片代理跳转错误:', error);
            next(error);
        }
    }
    static async getImageInfo(req, res, next) {
        try {
            const { imageId } = req.params;
            if (!imageId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的图片ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            let imageIdInt;
            if (/^\d+$/.test(imageId)) {
                imageIdInt = parseInt(imageId);
            }
            else {
                const internalId = await image_id_service_1.ImageIdService.getInternalId(imageId);
                if (!internalId) {
                    res.status(404).json({
                        success: false,
                        error: {
                            code: types_1.ErrorCodes.NOT_FOUND,
                            message: '图片不存在',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
                imageIdInt = internalId;
            }
            const image = await database_1.prisma.image.findUnique({
                where: { id: imageIdInt },
                include: {
                    imageLinks: {
                        include: {
                            provider: {
                                select: {
                                    id: true,
                                    name: true,
                                    description: true,
                                    status: true,
                                    isPremium: true
                                }
                            }
                        },
                        orderBy: [
                            { status: 'asc' },
                            { responseTime: 'asc' },
                            { errorCount: 'asc' }
                        ]
                    }
                }
            });
            if (!image) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '图片不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            res.json({
                success: true,
                data: {
                    imageId: image.id,
                    originalName: image.originalName,
                    fileHash: image.fileHash,
                    fileSize: image.fileSize.toString(),
                    mimeType: image.mimeType,
                    uploadStatus: image.uploadStatus,
                    createdAt: image.createdAt,
                    availableLinks: image.imageLinks.map(link => ({
                        id: link.id,
                        externalUrl: link.externalUrl,
                        status: link.status,
                        responseTime: link.responseTime,
                        lastChecked: link.lastChecked,
                        errorCount: link.errorCount,
                        provider: link.provider
                    }))
                }
            });
        }
        catch (error) {
            console.error('获取图片信息错误:', error);
            next(error);
        }
    }
    static async logAccess(imageId, ipAddress, userAgent, referer, providerId) {
        try {
            await database_1.prisma.accessLog.create({
                data: {
                    imageId,
                    ipAddress,
                    userAgent: userAgent || null,
                    referer: referer || null,
                    providerUsed: providerId || null
                }
            });
        }
        catch (error) {
            console.error('记录访问日志失败:', error);
        }
    }
}
exports.ImageProxyController = ImageProxyController;
//# sourceMappingURL=image-proxy.controller.js.map