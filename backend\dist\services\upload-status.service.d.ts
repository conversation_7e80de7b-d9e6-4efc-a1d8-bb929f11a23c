export interface UploadStatusInfo {
    imageId: number;
    publicId: string;
    originalName: string;
    uploadStatus: string;
    systemUrl: string;
    primaryLink?: {
        provider: string;
        url: string;
        status: string;
        responseTime?: number;
    };
    backupLinks: {
        provider: string;
        url: string;
        status: string;
        responseTime?: number;
    }[];
    backupProgress: {
        completed: number;
        total: number;
        percentage: number;
    };
    createdAt: string;
}
export declare class UploadStatusService {
    static getImageUploadStatus(imageId: number, userId: number): Promise<UploadStatusInfo | null>;
    static getUserUploadHistory(userId: number, page?: number, limit?: number): Promise<{
        uploads: UploadStatusInfo[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    static getBackupStats(imageId: number): Promise<{
        totalProviders: number;
        activeLinks: number;
        failedLinks: number;
        pendingUploads: number;
    }>;
}
//# sourceMappingURL=upload-status.service.d.ts.map