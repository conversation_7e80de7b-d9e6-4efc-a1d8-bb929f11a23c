import dotenv from 'dotenv';
import Joi from 'joi';

// 加载环境变量
dotenv.config();

// 环境变量验证模式
const envSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().default(3001),
  
  // 数据库配置
  DATABASE_URL: Joi.string().required(),
  
  // Redis配置
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_PASSWORD: Joi.string().optional(),
  
  // JWT配置
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('7d'),
  
  // 文件上传配置
  UPLOAD_DIR: Joi.string().default('./uploads'),
  MAX_FILE_SIZE: Joi.number().default(104857600), // 100MB
  ALLOWED_MIME_TYPES: Joi.string().default('image/jpeg,image/png,image/gif,image/webp'),
  
  // 前端URL
  FRONTEND_URL: Joi.string().default('http://localhost:5173'),
  
  // 监控配置
  ENABLE_METRICS: Joi.boolean().default(true),
  METRICS_INTERVAL: Joi.number().default(30000),
  
  // 安全配置
  ENABLE_IP_WARNING: Joi.boolean().default(true),
  ENABLE_RATE_LIMITING: Joi.boolean().default(true),
  RATE_LIMIT_WINDOW: Joi.number().default(900000), // 15分钟
  RATE_LIMIT_MAX: Joi.number().default(100),
  
  // 第三方接口配置
  IMGUR_CLIENT_ID: Joi.string().allow('').optional(),
  CLOUDINARY_CLOUD_NAME: Joi.string().allow('').optional(),
  CLOUDINARY_API_KEY: Joi.string().allow('').optional(),
  CLOUDINARY_API_SECRET: Joi.string().allow('').optional(),
  
  // 邮件配置
  SMTP_HOST: Joi.string().allow('').optional(),
  SMTP_PORT: Joi.alternatives().try(Joi.number(), Joi.string().allow('')).optional(),
  SMTP_USER: Joi.string().allow('').optional(),
  SMTP_PASS: Joi.string().allow('').optional(),
}).unknown();

// 验证环境变量
const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`环境变量配置错误: ${error.message}`);
}

// 导出配置
export const config = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  
  database: {
    url: envVars.DATABASE_URL,
  },
  
  redis: {
    host: envVars.REDIS_HOST,
    port: envVars.REDIS_PORT,
    password: envVars.REDIS_PASSWORD,
  },
  
  jwt: {
    secret: envVars.JWT_SECRET,
    expiresIn: envVars.JWT_EXPIRES_IN,
  },
  
  upload: {
    dir: envVars.UPLOAD_DIR,
    maxFileSize: envVars.MAX_FILE_SIZE,
    allowedMimeTypes: envVars.ALLOWED_MIME_TYPES.split(','),
  },
  
  frontend: {
    url: envVars.FRONTEND_URL,
  },
  
  monitoring: {
    enableMetrics: envVars.ENABLE_METRICS,
    metricsInterval: envVars.METRICS_INTERVAL,
  },
  
  security: {
    enableIpWarning: envVars.ENABLE_IP_WARNING,
    enableRateLimiting: envVars.ENABLE_RATE_LIMITING,
    rateLimitWindow: envVars.RATE_LIMIT_WINDOW,
    rateLimitMax: envVars.RATE_LIMIT_MAX,
  },
  
  providers: {
    imgur: {
      clientId: envVars.IMGUR_CLIENT_ID,
    },
    cloudinary: {
      cloudName: envVars.CLOUDINARY_CLOUD_NAME,
      apiKey: envVars.CLOUDINARY_API_KEY,
      apiSecret: envVars.CLOUDINARY_API_SECRET,
    },
  },
  
  email: {
    host: envVars.SMTP_HOST,
    port: envVars.SMTP_PORT,
    user: envVars.SMTP_USER,
    pass: envVars.SMTP_PASS,
  },
};

// 开发环境下打印配置信息
if (config.env === 'development') {
  console.log('📋 当前配置:', {
    env: config.env,
    port: config.port,
    redis: `${config.redis.host}:${config.redis.port}`,
    uploadDir: config.upload.dir,
    maxFileSize: `${Math.round(config.upload.maxFileSize / 1024 / 1024)}MB`,
  });
}
