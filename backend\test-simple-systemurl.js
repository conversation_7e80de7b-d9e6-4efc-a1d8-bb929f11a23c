/**
 * 简化的systemUrl测试
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testSystemUrlFormat() {
  console.log('🧪 开始测试systemUrl格式...\n');

  try {
    // 1. 直接查询数据库中的图片记录
    console.log('1️⃣ 查询数据库中的图片记录...');
    
    const images = await prisma.image.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        publicId: true,
        originalName: true,
        systemUrl: true,
        createdAt: true
      }
    });

    if (images.length === 0) {
      console.log('ℹ️ 数据库中暂无图片记录');
      return;
    }

    console.log(`✅ 找到 ${images.length} 条图片记录`);

    // 2. 检查systemUrl格式
    console.log('\n2️⃣ 检查systemUrl格式...');
    
    images.forEach((image, index) => {
      console.log(`\n  图片 ${index + 1}: ${image.originalName}`);
      console.log(`    ID: ${image.id}`);
      console.log(`    公开ID: ${image.publicId}`);
      console.log(`    systemUrl: ${image.systemUrl}`);
      
      // 检查URL格式
      if (image.systemUrl.startsWith('http://localhost:3000/image/show/')) {
        console.log('    ✅ systemUrl格式正确 - 包含完整域名');
      } else if (image.systemUrl.startsWith('/image/show/')) {
        console.log('    ❌ systemUrl格式错误 - 缺少域名前缀');
        console.log('    💡 需要在API中添加域名前缀');
      } else if (image.systemUrl.startsWith('http')) {
        console.log('    ✅ systemUrl格式正确 - 包含完整域名');
      } else {
        console.log('    ❌ systemUrl格式异常 - 未知格式');
      }
      
      // 检查publicId格式
      if (image.publicId && image.publicId.length === 16) {
        console.log('    ✅ publicId格式正确 - 16位字符串');
      } else {
        console.log(`    ❌ publicId格式错误 - 长度: ${image.publicId?.length || 0}`);
      }
    });

    // 3. 模拟API处理逻辑
    console.log('\n3️⃣ 模拟API处理逻辑...');
    
    const baseUrl = 'http://localhost:3000';
    
    images.forEach((image, index) => {
      const originalUrl = image.systemUrl;
      const processedUrl = originalUrl.startsWith('http') 
        ? originalUrl 
        : `${baseUrl}${originalUrl}`;
      
      console.log(`\n  图片 ${index + 1} URL处理:`);
      console.log(`    原始URL: ${originalUrl}`);
      console.log(`    处理后URL: ${processedUrl}`);
      
      if (processedUrl.startsWith('http://localhost:3000/image/show/')) {
        console.log('    ✅ 处理后格式正确');
      } else {
        console.log('    ❌ 处理后格式仍有问题');
      }
    });

    // 4. 检查用户图片关联
    console.log('\n4️⃣ 检查用户图片关联...');
    
    const userImages = await prisma.userImage.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            username: true,
            email: true,
            userLevel: true
          }
        },
        image: {
          select: {
            originalName: true,
            systemUrl: true,
            publicId: true
          }
        }
      }
    });

    console.log(`✅ 找到 ${userImages.length} 条用户图片关联记录`);
    
    userImages.forEach((userImage, index) => {
      console.log(`\n  关联 ${index + 1}:`);
      console.log(`    用户: ${userImage.user.username} (${userImage.user.userLevel})`);
      console.log(`    文件: ${userImage.image.originalName}`);
      console.log(`    systemUrl: ${userImage.image.systemUrl}`);
      
      // 模拟管理端API处理
      const baseUrl = 'http://localhost:3000';
      const processedUrl = userImage.image.systemUrl.startsWith('http') 
        ? userImage.image.systemUrl 
        : `${baseUrl}${userImage.image.systemUrl}`;
      
      console.log(`    管理端处理后: ${processedUrl}`);
    });

    console.log('\n🎉 systemUrl格式测试完成！');

    // 5. 生成修复建议
    console.log('\n💡 修复建议:');
    console.log('=' .repeat(50));
    
    const hasIncompleteUrls = images.some(img => !img.systemUrl.startsWith('http'));
    
    if (hasIncompleteUrls) {
      console.log('📋 发现问题:');
      console.log('   - 数据库中的systemUrl缺少域名前缀');
      console.log('   - 需要在API响应中添加完整域名');
      console.log('');
      console.log('🔧 修复方案:');
      console.log('   1. 在管理端API中添加域名前缀处理');
      console.log('   2. 确保用户端和管理端格式一致');
      console.log('   3. 考虑更新数据库中的systemUrl格式');
    } else {
      console.log('✅ systemUrl格式正常，无需修复');
    }
    
    console.log('=' .repeat(50));

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  testSystemUrlFormat()
    .then(() => {
      console.log('\n✨ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testSystemUrlFormat };
