export declare class ImageIdService {
    private static idMap;
    private static reverseMap;
    static generatePublicId(imageId: number): Promise<string>;
    static getInternalId(publicId: string): Promise<number | null>;
    private static generateConsistentId;
    private static simpleHash;
    static generatePublicIdsForExistingImages(): Promise<void>;
    static clearCache(): void;
    static getCacheStats(): {
        mappings: number;
        reverseMappings: number;
    };
}
//# sourceMappingURL=image-id.service.d.ts.map