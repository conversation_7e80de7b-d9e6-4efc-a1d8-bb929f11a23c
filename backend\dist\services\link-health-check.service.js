"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkHealthCheckService = void 0;
const database_1 = require("../config/database");
const axios_1 = __importDefault(require("axios"));
class LinkHealthCheckService {
    static startPeriodicHealthCheck() {
        console.log('🔍 启动链接健康检查服务');
        LinkHealthCheckService.performHealthCheck();
        LinkHealthCheckService.checkTimer = setInterval(() => {
            LinkHealthCheckService.performHealthCheck();
        }, LinkHealthCheckService.CHECK_INTERVAL);
    }
    static stopPeriodicHealthCheck() {
        if (LinkHealthCheckService.checkTimer) {
            clearInterval(LinkHealthCheckService.checkTimer);
            LinkHealthCheckService.checkTimer = null;
            console.log('⏹️ 链接健康检查服务已停止');
        }
    }
    static async performHealthCheck() {
        try {
            console.log('🔍 开始执行链接健康检查...');
            const links = await database_1.prisma.imageLink.findMany({
                where: {
                    OR: [
                        { lastChecked: null },
                        {
                            lastChecked: {
                                lt: new Date(Date.now() - LinkHealthCheckService.CHECK_INTERVAL)
                            }
                        }
                    ]
                },
                include: {
                    provider: {
                        select: {
                            id: true,
                            name: true,
                            status: true
                        }
                    }
                },
                orderBy: [
                    { lastChecked: 'asc' },
                    { errorCount: 'desc' }
                ],
                take: 100
            });
            if (links.length === 0) {
                console.log('✅ 没有需要检查的链接');
                return;
            }
            console.log(`🔍 找到 ${links.length} 个链接需要检查`);
            const batches = LinkHealthCheckService.chunkArray(links, LinkHealthCheckService.MAX_CONCURRENT_CHECKS);
            let totalChecked = 0;
            let healthyCount = 0;
            let unhealthyCount = 0;
            for (const batch of batches) {
                const results = await Promise.allSettled(batch.map(link => LinkHealthCheckService.checkSingleLink(link)));
                for (let i = 0; i < results.length; i++) {
                    const result = results[i];
                    const link = batch[i];
                    if (!result || !link)
                        continue;
                    if (result.status === 'fulfilled') {
                        const checkResult = result.value;
                        await LinkHealthCheckService.updateLinkStatus(link.id, checkResult);
                        if (checkResult.isHealthy) {
                            healthyCount++;
                        }
                        else {
                            unhealthyCount++;
                        }
                    }
                    else if (result.status === 'rejected') {
                        console.error(`检查链接 ${link.id} 时发生错误:`, result.reason);
                        await LinkHealthCheckService.updateLinkStatus(link.id, {
                            linkId: link.id,
                            isHealthy: false,
                            error: '检查过程中发生错误'
                        });
                        unhealthyCount++;
                    }
                    totalChecked++;
                }
                if (batches.indexOf(batch) < batches.length - 1) {
                    await LinkHealthCheckService.sleep(1000);
                }
            }
            console.log(`✅ 健康检查完成: 总计 ${totalChecked} 个链接，健康 ${healthyCount} 个，异常 ${unhealthyCount} 个`);
        }
        catch (error) {
            console.error('❌ 执行健康检查时发生错误:', error);
        }
    }
    static async checkSingleLink(link) {
        const startTime = Date.now();
        try {
            const response = await axios_1.default.head(link.externalUrl, {
                timeout: LinkHealthCheckService.TIMEOUT,
                validateStatus: (status) => status < 500,
                maxRedirects: 5,
                headers: {
                    'User-Agent': 'LoftChat-HealthChecker/1.0'
                }
            });
            const responseTime = Date.now() - startTime;
            const isHealthy = response.status >= 200 && response.status < 400;
            return {
                linkId: link.id,
                isHealthy,
                responseTime,
                statusCode: response.status
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                linkId: link.id,
                isHealthy: false,
                responseTime,
                error: error.message || '未知错误'
            };
        }
    }
    static async updateLinkStatus(linkId, result) {
        try {
            const currentLink = await database_1.prisma.imageLink.findUnique({
                where: { id: linkId }
            });
            if (!currentLink)
                return;
            const newStatus = result.isHealthy ? 'active' : 'failed';
            const newErrorCount = result.isHealthy ? 0 : (currentLink.errorCount + 1);
            await database_1.prisma.imageLink.update({
                where: { id: linkId },
                data: {
                    status: newStatus,
                    responseTime: result.responseTime || null,
                    lastChecked: new Date(),
                    errorCount: newErrorCount
                }
            });
            if (newErrorCount >= 5) {
                console.warn(`⚠️ 链接 ${linkId} 连续失败 ${newErrorCount} 次，可能需要人工检查`);
            }
        }
        catch (error) {
            console.error(`更新链接 ${linkId} 状态时发生错误:`, error);
        }
    }
    static async checkImageLinks(imageId) {
        const links = await database_1.prisma.imageLink.findMany({
            where: { imageId },
            include: {
                provider: {
                    select: {
                        id: true,
                        name: true,
                        status: true
                    }
                }
            }
        });
        const results = [];
        for (const link of links) {
            const result = await LinkHealthCheckService.checkSingleLink(link);
            await LinkHealthCheckService.updateLinkStatus(link.id, result);
            results.push(result);
        }
        return results;
    }
    static async getHealthStats() {
        const stats = await database_1.prisma.imageLink.groupBy({
            by: ['status'],
            _count: {
                id: true
            }
        });
        const totalLinks = await database_1.prisma.imageLink.count();
        const recentlyChecked = await database_1.prisma.imageLink.count({
            where: {
                lastChecked: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
                }
            }
        });
        const avgResponseTime = await database_1.prisma.imageLink.aggregate({
            _avg: {
                responseTime: true
            },
            where: {
                responseTime: {
                    not: null
                },
                status: 'active'
            }
        });
        return {
            total: totalLinks,
            recentlyChecked,
            statusDistribution: stats.reduce((acc, stat) => {
                acc[stat.status] = stat._count.id;
                return acc;
            }, {}),
            averageResponseTime: Math.round(avgResponseTime._avg.responseTime || 0),
            lastCheckTime: new Date().toISOString()
        };
    }
    static chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.LinkHealthCheckService = LinkHealthCheckService;
LinkHealthCheckService.TIMEOUT = 10000;
LinkHealthCheckService.MAX_CONCURRENT_CHECKS = 5;
LinkHealthCheckService.CHECK_INTERVAL = 30 * 60 * 1000;
LinkHealthCheckService.checkTimer = null;
//# sourceMappingURL=link-health-check.service.js.map