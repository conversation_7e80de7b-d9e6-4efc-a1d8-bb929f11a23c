/**
 * 测试图片跳转配置API
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testRedirectConfigAPI() {
  console.log('🧪 开始测试图片跳转配置API...\n');

  try {
    // 1. 使用管理员账户登录
    console.log('1️⃣ 管理员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('管理员登录失败: ' + loginResponse.data.error?.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 管理员登录成功');

    // 2. 测试获取可用提供商
    console.log('\n2️⃣ 测试获取可用提供商...');
    const providersResponse = await axios.get(`${BASE_URL}/api/image-redirect-config/providers`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (providersResponse.data.success) {
      console.log('✅ 获取提供商成功');
      console.log('📋 可用提供商:');
      providersResponse.data.data.providers.forEach(provider => {
        console.log(`   - ${provider.displayName} (ID: ${provider.id})`);
      });
    } else {
      console.log('❌ 获取提供商失败:', providersResponse.data.error?.message);
    }

    // 3. 测试获取用户全局配置
    console.log('\n3️⃣ 测试获取用户全局配置...');
    const configResponse = await axios.get(`${BASE_URL}/api/image-redirect-config/global`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (configResponse.data.success) {
      console.log('✅ 获取全局配置成功');
      console.log('📋 当前配置:', JSON.stringify(configResponse.data.data, null, 2));
    } else {
      console.log('❌ 获取全局配置失败:', configResponse.data.error?.message);
    }

    // 4. 测试保存用户全局配置 - 自动模式
    console.log('\n4️⃣ 测试保存全局配置 - 自动模式...');
    const autoConfigResponse = await axios.post(`${BASE_URL}/api/image-redirect-config/global`, {
      redirectMode: 'auto',
      isActive: true
    }, {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (autoConfigResponse.data.success) {
      console.log('✅ 保存自动模式配置成功');
      console.log('📋 保存结果:', JSON.stringify(autoConfigResponse.data.data, null, 2));
    } else {
      console.log('❌ 保存自动模式配置失败:', autoConfigResponse.data.error?.message);
    }

    // 5. 测试保存用户全局配置 - 手动模式
    console.log('\n5️⃣ 测试保存全局配置 - 手动模式...');
    
    // 获取第一个可用提供商的ID
    let preferredProviderId = null;
    if (providersResponse.data.success && providersResponse.data.data.providers.length > 0) {
      preferredProviderId = providersResponse.data.data.providers[0].id;
    }

    if (preferredProviderId) {
      const manualConfigResponse = await axios.post(`${BASE_URL}/api/image-redirect-config/global`, {
        redirectMode: 'manual',
        preferredProviderId: preferredProviderId,
        isActive: true
      }, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (manualConfigResponse.data.success) {
        console.log('✅ 保存手动模式配置成功');
        console.log('📋 保存结果:', JSON.stringify(manualConfigResponse.data.data, null, 2));
      } else {
        console.log('❌ 保存手动模式配置失败:', manualConfigResponse.data.error?.message);
      }
    } else {
      console.log('⚠️ 跳过手动模式测试：没有可用的提供商');
    }

    // 6. 验证配置是否正确保存
    console.log('\n6️⃣ 验证配置是否正确保存...');
    const verifyResponse = await axios.get(`${BASE_URL}/api/image-redirect-config/global`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (verifyResponse.data.success) {
      console.log('✅ 验证配置成功');
      console.log('📋 最终配置:', JSON.stringify(verifyResponse.data.data, null, 2));
      
      const finalConfig = verifyResponse.data.data;
      if (finalConfig.redirectMode === 'manual' && finalConfig.preferredProviderId === preferredProviderId) {
        console.log('✅ 配置保存验证通过');
      } else {
        console.log('❌ 配置保存验证失败');
      }
    } else {
      console.log('❌ 验证配置失败:', verifyResponse.data.error?.message);
    }

    // 7. 测试错误情况
    console.log('\n7️⃣ 测试错误情况...');
    
    // 测试无效的跳转模式
    try {
      await axios.post(`${BASE_URL}/api/image-redirect-config/global`, {
        redirectMode: 'invalid',
        isActive: true
      }, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('❌ 应该拒绝无效的跳转模式');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 正确拒绝了无效的跳转模式');
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }

    // 测试无效的提供商ID
    try {
      await axios.post(`${BASE_URL}/api/image-redirect-config/global`, {
        redirectMode: 'manual',
        preferredProviderId: 99999,
        isActive: true
      }, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('❌ 应该拒绝无效的提供商ID');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 正确拒绝了无效的提供商ID');
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }

    console.log('\n🎉 图片跳转配置API测试完成！');

    // 8. 生成测试报告
    console.log('\n📊 测试报告:');
    console.log('=' .repeat(50));
    console.log('✅ 获取可用提供商API正常');
    console.log('✅ 获取用户全局配置API正常');
    console.log('✅ 保存用户全局配置API正常');
    console.log('✅ 自动模式配置正常');
    console.log('✅ 手动模式配置正常');
    console.log('✅ 配置验证正常');
    console.log('✅ 错误处理正常');
    console.log('=' .repeat(50));

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('📊 响应状态:', error.response.status);
      console.error('📋 响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
if (require.main === module) {
  testRedirectConfigAPI()
    .then(() => {
      console.log('\n✨ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testRedirectConfigAPI };
