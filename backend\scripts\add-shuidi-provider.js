/**
 * 添加水滴云上传接口配置脚本
 * 执行时间: 2024-07-06
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addShuidiProvider() {
  try {
    console.log('🚀 开始添加水滴云上传接口配置...\n');

    // 检查是否已存在水滴云接口
    const existingProvider = await prisma.uploadProvider.findFirst({
      where: { 
        OR: [
          { name: '水滴云' },
          { name: '<PERSON><PERSON><PERSON>' },
          { endpoint: 'https://upload.shuidi.cn/uploadimage' }
        ]
      }
    });

    if (existingProvider) {
      console.log('⚠️  水滴云接口已存在，跳过创建');
      console.log('现有配置:', {
        id: existingProvider.id,
        name: existingProvider.name,
        endpoint: existingProvider.endpoint,
        status: existingProvider.status
      });
      return existingProvider;
    }

    // 插入水滴云接口配置
    const shuidiProvider = await prisma.uploadProvider.create({
      data: {
        name: '水滴云',
        description: '水滴云图片上传服务 - 高可用CDN加速',
        endpoint: 'https://upload.shuidi.cn/uploadimage',
        apiKey: null, // 无需API密钥
        config: {
          baseUrl: 'https://filehuoshan.shuidi.cn/img/',
          suffix: '/0x0.jpg'
        }, // 配置基础URL和后缀
        status: 'active',
        priority: 11, // 优先级设为11，低于TCL
        maxFileSize: BigInt(20971520), // 20MB最大文件大小
        supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'], // 支持的图片格式
        requiredLevel: 'free', // 免费用户即可使用
        isPremium: false, // 非高级接口
        costPerUpload: 0.0000, // 无上传成本
      }
    });

    console.log('✅ 水滴云接口配置创建成功');
    console.log('📋 配置详情:');
    console.log(`   ID: ${shuidiProvider.id}`);
    console.log(`   名称: ${shuidiProvider.name}`);
    console.log(`   端点: ${shuidiProvider.endpoint}`);
    console.log(`   状态: ${shuidiProvider.status}`);
    console.log(`   优先级: ${shuidiProvider.priority}`);
    console.log(`   最大文件大小: ${Number(shuidiProvider.maxFileSize) / 1024 / 1024}MB`);

    // 为所有用户等级添加水滴云接口可见性配置
    console.log('\n🔧 配置用户等级可见性...');
    
    const levels = ['free', 'vip1', 'vip2', 'vip3', 'admin'];
    const visibilityConfigs = [];

    for (const level of levels) {
      const config = await prisma.levelProviderVisibility.create({
        data: {
          level: level,
          providerId: shuidiProvider.id,
          isVisible: true,
          displayOrder: 11, // 显示顺序与优先级一致
        }
      });
      visibilityConfigs.push(config);
      console.log(`   ✅ ${level} 用户可见性配置完成`);
    }

    // 验证可见性配置
    console.log('\n🔍 验证可见性配置:');
    const verifyConfigs = await prisma.levelProviderVisibility.findMany({
      where: {
        providerId: shuidiProvider.id
      },
      include: {
        provider: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        level: 'asc'
      }
    });

    verifyConfigs.forEach(config => {
      console.log(`  ${config.level}: ${config.isVisible ? '✅ 可见' : '❌ 不可见'} (显示顺序: ${config.displayOrder})`);
    });

    console.log('\n🎉 水滴云接口配置完成！');
    return shuidiProvider;

  } catch (error) {
    console.error('❌ 添加水滴云接口配置失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 检查并恢复数据的函数
async function checkAndRestoreData() {
  try {
    console.log('🔍 检查现有数据...\n');

    // 检查所有上传接口
    const allProviders = await prisma.uploadProvider.findMany({
      orderBy: { priority: 'asc' }
    });

    console.log('📋 当前系统中的上传接口:');
    allProviders.forEach(provider => {
      const statusIcon = provider.status === 'active' ? '✅' : '❌';
      console.log(`${statusIcon} ${provider.name} (ID: ${provider.id}, 优先级: ${provider.priority})`);
    });

    // 检查TCL接口是否存在
    const tclProvider = allProviders.find(p => p.name === 'TCL');
    if (!tclProvider) {
      console.log('\n⚠️  检测到TCL接口丢失，正在恢复...');
      
      // 恢复TCL接口
      const restoredTcl = await prisma.uploadProvider.create({
        data: {
          name: 'TCL',
          description: 'TCL第三方上传接口 - 非常规接口实现',
          endpoint: 'https://service2.tcl.com/api.php/Center/uploadQiniu',
          apiKey: null,
          config: {},
          status: 'active',
          priority: 10,
          maxFileSize: BigInt(10485760),
          supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
          requiredLevel: 'free',
          isPremium: false,
          costPerUpload: 0.0000,
        }
      });

      // 恢复TCL的可见性配置
      const levels = ['free', 'vip1', 'vip2', 'vip3', 'admin'];
      for (const level of levels) {
        await prisma.levelProviderVisibility.create({
          data: {
            level: level,
            providerId: restoredTcl.id,
            isVisible: true,
            displayOrder: 10,
          }
        });
      }

      console.log('✅ TCL接口已恢复');
    } else {
      console.log('\n✅ TCL接口存在，无需恢复');
    }

    console.log(`\n📊 总计: ${allProviders.length} 个上传接口`);

  } catch (error) {
    console.error('❌ 数据检查失败:', error);
    throw error;
  }
}

// 执行脚本
if (require.main === module) {
  (async () => {
    try {
      // 先检查并恢复数据
      await checkAndRestoreData();
      
      // 然后添加水滴云接口
      await addShuidiProvider();
      
      console.log('\n✨ 脚本执行完成');
      process.exit(0);
    } catch (error) {
      console.error('\n💥 脚本执行失败:', error);
      process.exit(1);
    }
  })();
}

module.exports = { addShuidiProvider, checkAndRestoreData };
