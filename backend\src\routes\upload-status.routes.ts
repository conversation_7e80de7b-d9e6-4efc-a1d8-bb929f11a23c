/**
 * 上传状态路由
 * 提供图片上传状态和备份进度的API路由
 */

import { Router } from 'express';
import { UploadStatusController } from '../controllers/upload-status.controller';
import { authenticateToken, requestId, recordIP } from '../middleware/auth.middleware';
import { param, query } from 'express-validator';
import { validateRequest } from '../middleware/validation.middleware';

const router = Router();

// 应用通用中间件
router.use(requestId);
router.use(recordIP);
router.use(authenticateToken);

/**
 * @route   GET /api/upload-status/image/:imageId
 * @desc    获取图片的详细上传状态
 * @access  Private
 */
router.get('/image/:imageId',
  param('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'),
  validateRequest,
  UploadStatusController.getImageStatus
);

/**
 * @route   GET /api/upload-status/history
 * @desc    获取用户的上传历史（包含备份状态）
 * @access  Private
 */
router.get('/history',
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  validateRequest,
  UploadStatusController.getUploadHistory
);

/**
 * @route   GET /api/upload-status/image/:imageId/backup-stats
 * @desc    获取图片的备份统计信息
 * @access  Private
 */
router.get('/image/:imageId/backup-stats',
  param('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'),
  validateRequest,
  UploadStatusController.getBackupStats
);

/**
 * @route   GET /api/upload-status/providers-overview
 * @desc    获取系统的上传接口状态概览
 * @access  Private
 */
router.get('/providers-overview',
  UploadStatusController.getProvidersOverview
);

export default router;
