/**
 * 水滴云上传接口适配器
 * 基于PHP代码实现的Node.js版本
 * 
 * 原始PHP接口信息:
 * - 接口地址: https://upload.shuidi.cn/uploadimage
 * - 请求方式: POST multipart/form-data
 * - 文件字段: file
 * - 响应处理: 从JSON响应中提取path字段，拼接为完整URL
 */

import axios from 'axios';
import FormData from 'form-data';
import { BaseProvider, ProviderConfig, UploadResult, HealthCheckResult } from './base.provider';

export class ShuidiProvider extends BaseProvider {
  constructor(config: ProviderConfig) {
    super(config);
  }

  /**
   * 上传文件到水滴云
   */
  async upload(fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      // 验证文件
      const validation = this.validateFile(fileBuffer, fileName, mimeType);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error || '文件验证失败',
          providerId: this.config.id,
          providerName: this.config.name,
        };
      }

      // 构建FormData
      const formData = new FormData();
      formData.append('file', fileBuffer, {
        filename: fileName,
        contentType: mimeType
      });

      // 设置请求头
      const headers: any = {
        ...formData.getHeaders(),
      };

      // 如果有API密钥，添加到请求头
      if (this.config.apiKey) {
        headers['Authorization'] = `Bearer ${this.config.apiKey}`;
      }

      // 发送请求到水滴云接口
      const response = await axios.post(this.config.endpoint, formData, {
        headers,
        timeout: 30000, // 30秒超时
        maxContentLength: this.config.maxFileSize,
        maxBodyLength: this.config.maxFileSize,
      });

      const responseTime = Date.now() - startTime;

      // 解析响应数据
      if (response.data && response.data.path) {
        // 按照PHP代码的逻辑，拼接完整的URL
        const fullUrl = `https://filehuoshan.shuidi.cn/img/${response.data.path}/0x0.jpg`;

        return {
          success: true,
          url: fullUrl,
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
          metadata: {
            originalPath: response.data.path,
            rawResponse: response.data
          }
        };
      } else {
        return {
          success: false,
          error: '水滴云接口响应中未找到path字段',
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
        };
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      if (axios.isAxiosError(error)) {
        let errorMessage = '水滴云上传失败';
        
        if (error.response) {
          // 服务器响应了错误状态码
          errorMessage = `水滴云服务器错误 (${error.response.status}): ${error.response.statusText}`;
        } else if (error.request) {
          // 请求发出但没有收到响应
          errorMessage = '水滴云服务器无响应，请检查网络连接';
        } else {
          // 请求配置错误
          errorMessage = `请求配置错误: ${error.message}`;
        }

        return {
          success: false,
          error: errorMessage,
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
        };
      }

      return {
        success: false,
        error: `水滴云上传异常: ${error instanceof Error ? error.message : '未知错误'}`,
        providerId: this.config.id,
        providerName: this.config.name,
        responseTime,
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      // 创建一个1x1像素的测试图片
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
      ]);

      const result = await this.upload(testImageBuffer, 'health-check.png', 'image/png');
      const responseTime = Date.now() - startTime;

      const healthResult: HealthCheckResult = {
        isHealthy: result.success,
        responseTime,
        lastChecked: new Date()
      };

      if (!result.success && result.error) {
        healthResult.error = result.error;
      }

      return healthResult;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      const errorMessage = error instanceof Error ? error.message : '健康检查失败';
      return {
        isHealthy: false,
        responseTime,
        error: errorMessage,
        lastChecked: new Date()
      };
    }
  }

  /**
   * 获取接口特定的配置信息
   */
  getProviderInfo() {
    return {
      name: '水滴云',
      description: '水滴云图片上传服务',
      features: [
        '支持常见图片格式',
        '自动生成CDN链接',
        '高可用性保障'
      ],
      limitations: [
        '仅支持图片文件',
        '单文件大小限制',
        '需要稳定网络连接'
      ]
    };
  }
}
