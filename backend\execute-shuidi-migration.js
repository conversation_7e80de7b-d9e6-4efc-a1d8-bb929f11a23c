const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function executeMigration() {
  try {
    console.log('🚀 开始执行水滴云接口迁移...\n');

    // 检查并恢复TCL接口
    console.log('1️⃣ 检查TCL接口...');
    const tclExists = await prisma.uploadProvider.findFirst({
      where: { name: 'TCL' }
    });

    if (!tclExists) {
      console.log('⚠️  TCL接口不存在，正在恢复...');
      
      const tclProvider = await prisma.uploadProvider.create({
        data: {
          name: 'TCL',
          description: 'TCL第三方上传接口 - 非常规接口实现',
          endpoint: 'https://service2.tcl.com/api.php/Center/uploadQiniu',
          apiKey: null,
          config: {},
          status: 'active',
          priority: 10,
          maxFileSize: BigInt(10485760),
          supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
          requiredLevel: 'free',
          isPremium: false,
          costPerUpload: 0.0000,
        }
      });

      // 添加TCL可见性配置
      const levels = ['free', 'vip1', 'vip2', 'vip3', 'admin'];
      for (const level of levels) {
        await prisma.levelProviderVisibility.create({
          data: {
            level: level,
            providerId: tclProvider.id,
            isVisible: true,
            displayOrder: 10,
          }
        });
      }

      console.log('✅ TCL接口已恢复');
    } else {
      console.log('✅ TCL接口已存在');
    }

    // 检查水滴云接口
    console.log('\n2️⃣ 检查水滴云接口...');
    const shuidiExists = await prisma.uploadProvider.findFirst({
      where: { 
        OR: [
          { name: '水滴云' },
          { endpoint: 'https://upload.shuidi.cn/uploadimage' }
        ]
      }
    });

    if (shuidiExists) {
      console.log('⚠️  水滴云接口已存在，跳过创建');
      console.log('现有配置:', {
        id: shuidiExists.id,
        name: shuidiExists.name,
        endpoint: shuidiExists.endpoint,
        status: shuidiExists.status
      });
    } else {
      console.log('🔧 创建水滴云接口...');
      
      const shuidiProvider = await prisma.uploadProvider.create({
        data: {
          name: '水滴云',
          description: '水滴云图片上传服务 - 高可用CDN加速',
          endpoint: 'https://upload.shuidi.cn/uploadimage',
          apiKey: null,
          config: {
            baseUrl: 'https://filehuoshan.shuidi.cn/img/',
            suffix: '/0x0.jpg'
          },
          status: 'active',
          priority: 11,
          maxFileSize: BigInt(20971520), // 20MB
          supportedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
          requiredLevel: 'free',
          isPremium: false,
          costPerUpload: 0.0000,
        }
      });

      // 添加水滴云可见性配置
      const levels = ['free', 'vip1', 'vip2', 'vip3', 'admin'];
      for (const level of levels) {
        await prisma.levelProviderVisibility.create({
          data: {
            level: level,
            providerId: shuidiProvider.id,
            isVisible: true,
            displayOrder: 11,
          }
        });
      }

      console.log('✅ 水滴云接口创建成功');
      console.log('配置详情:', {
        id: shuidiProvider.id,
        name: shuidiProvider.name,
        endpoint: shuidiProvider.endpoint,
        priority: shuidiProvider.priority,
        maxFileSize: `${Number(shuidiProvider.maxFileSize) / 1024 / 1024}MB`
      });
    }

    // 验证最终结果
    console.log('\n3️⃣ 验证配置结果...');
    const allProviders = await prisma.uploadProvider.findMany({
      where: {
        name: { in: ['TCL', '水滴云'] }
      },
      orderBy: { priority: 'asc' }
    });

    console.log('📋 相关接口配置:');
    allProviders.forEach(provider => {
      const statusIcon = provider.status === 'active' ? '✅' : '❌';
      console.log(`${statusIcon} ${provider.name} (ID: ${provider.id}, 优先级: ${provider.priority})`);
    });

    console.log('\n🎉 迁移执行完成！');

  } catch (error) {
    console.error('❌ 迁移执行失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

executeMigration();
