{"version": 3, "file": "shuidi.provider.js", "sourceRoot": "", "sources": ["../../src/providers/shuidi.provider.ts"], "names": [], "mappings": ";;;;;;AAWA,kDAA0B;AAC1B,0DAAiC;AACjC,mDAAgG;AAEhG,MAAa,cAAe,SAAQ,4BAAY;IAC9C,YAAY,MAAsB;QAChC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,QAAQ;oBACnC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;iBAC/B,CAAC;YACJ,CAAC;YAGD,MAAM,QAAQ,GAAG,IAAI,mBAAQ,EAAE,CAAC;YAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;gBAClC,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,QAAQ;aACtB,CAAC,CAAC;YAGH,MAAM,OAAO,GAAQ;gBACnB,GAAG,QAAQ,CAAC,UAAU,EAAE;aACzB,CAAC;YAGF,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACvB,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5D,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,EAAE;gBAChE,OAAO;gBACP,OAAO,EAAE,KAAK;gBACd,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;gBACzC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;aACvC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG5C,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBAExC,MAAM,OAAO,GAAG,qCAAqC,QAAQ,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC;gBAElF,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,GAAG,EAAE,OAAO;oBACZ,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBAC9B,YAAY;oBACZ,QAAQ,EAAE;wBACR,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;wBAChC,WAAW,EAAE,QAAQ,CAAC,IAAI;qBAC3B;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mBAAmB;oBAC1B,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBAC9B,YAAY;iBACb,CAAC;YACJ,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,YAAY,GAAG,SAAS,CAAC;gBAE7B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBAEnB,YAAY,GAAG,aAAa,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACrF,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;oBAEzB,YAAY,GAAG,mBAAmB,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBAEN,YAAY,GAAG,WAAW,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC5C,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,YAAY;oBACnB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBAC9B,YAAY;iBACb,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE;gBACpE,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC9B,YAAY;aACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC;gBAClC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;gBACtE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;gBACtE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;gBACtE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;gBACtE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;aACvE,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;YACnF,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,SAAS,EAAE,MAAM,CAAC,OAAO;gBACzB,YAAY;gBACZ,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK;gBAChD,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,YAAY;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;gBACxD,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,eAAe;QACb,OAAO;YACL,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,WAAW;YACxB,QAAQ,EAAE;gBACR,UAAU;gBACV,WAAW;gBACX,QAAQ;aACT;YACD,WAAW,EAAE;gBACX,SAAS;gBACT,SAAS;gBACT,UAAU;aACX;SACF,CAAC;IACJ,CAAC;CACF;AAzKD,wCAyKC"}