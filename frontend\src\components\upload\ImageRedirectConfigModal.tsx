import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';

interface Provider {
  id: number;
  name: string;
  description: string;
  status: string;
  isPremium: boolean;
}

interface RedirectConfig {
  redirectStrategy: 'auto' | 'provider' | 'priority';
  preferredProviderId?: number;
  fallbackStrategy: 'auto' | 'priority';
  isEnabled: boolean;
}

interface ImageRedirectConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageId: number;
  imageName: string;
}

export function ImageRedirectConfigModal({ 
  isOpen, 
  onClose, 
  imageId, 
  imageName 
}: ImageRedirectConfigModalProps) {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [config, setConfig] = useState<RedirectConfig>({
    redirectStrategy: 'auto',
    preferredProviderId: undefined,
    fallbackStrategy: 'auto',
    isEnabled: true
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadProviders();
      loadCurrentConfig();
    }
  }, [isOpen, imageId]);

  const loadProviders = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/image-redirect-config/providers', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setProviders(data.data.providers || []);
        }
      }
    } catch (error) {
      console.error('加载接口列表失败:', error);
    }
  };

  const loadCurrentConfig = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/image-redirect-config/${imageId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setConfig({
            redirectStrategy: data.data.redirectStrategy || 'auto',
            preferredProviderId: data.data.preferredProviderId || undefined,
            fallbackStrategy: data.data.fallbackStrategy || 'auto',
            isEnabled: data.data.isEnabled !== undefined ? data.data.isEnabled : true
          });
        }
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/image-redirect-config/${imageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          alert('配置保存成功！');
          onClose();
        } else {
          throw new Error(data.error?.message || '保存失败');
        }
      } else {
        throw new Error('保存失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      alert(error instanceof Error ? error.message : '保存失败');
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <Card className="border-0 shadow-lg">
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>图片跳转设置</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  为图片 "{imageName}" 配置专属跳转策略
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="loading-spinner w-6 h-6 mx-auto mb-2" />
                <p className="text-gray-600">加载配置中...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* 启用开关 */}
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900">启用专属配置</h3>
                    <p className="text-sm text-gray-600">为此图片使用独立的跳转策略</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={config.isEnabled}
                      onChange={(e) => setConfig({ ...config, isEnabled: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {config.isEnabled && (
                  <>
                    {/* 跳转策略 */}
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">跳转策略</h3>
                      <div className="space-y-3">
                        <label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="redirectStrategy"
                            value="auto"
                            checked={config.redirectStrategy === 'auto'}
                            onChange={(e) => setConfig({ ...config, redirectStrategy: e.target.value as any })}
                            className="text-blue-600"
                          />
                          <div>
                            <div className="font-medium">自动最优</div>
                            <div className="text-sm text-gray-600">系统自动选择最快、最稳定的链接</div>
                          </div>
                        </label>

                        <label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="redirectStrategy"
                            value="provider"
                            checked={config.redirectStrategy === 'provider'}
                            onChange={(e) => setConfig({ ...config, redirectStrategy: e.target.value as any })}
                            className="text-blue-600"
                          />
                          <div>
                            <div className="font-medium">指定接口</div>
                            <div className="text-sm text-gray-600">优先使用指定的上传接口</div>
                          </div>
                        </label>

                        <label className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="redirectStrategy"
                            value="priority"
                            checked={config.redirectStrategy === 'priority'}
                            onChange={(e) => setConfig({ ...config, redirectStrategy: e.target.value as any })}
                            className="text-blue-600"
                          />
                          <div>
                            <div className="font-medium">优先级顺序</div>
                            <div className="text-sm text-gray-600">按照接口优先级依次尝试</div>
                          </div>
                        </label>
                      </div>
                    </div>

                    {/* 指定接口选择 */}
                    {config.redirectStrategy === 'provider' && (
                      <div>
                        <h3 className="font-medium text-gray-900 mb-3">选择优先接口</h3>
                        <select
                          value={config.preferredProviderId?.toString() || ''}
                          onChange={(e) => setConfig({
                            ...config,
                            preferredProviderId: e.target.value ? parseInt(e.target.value) : undefined
                          })}
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">请选择接口</option>
                          {providers.map(provider => (
                            <option key={provider.id} value={provider.id}>
                              {provider.name} {provider.isPremium ? '(高级)' : ''}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}

                    {/* 备用策略 */}
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">备用策略</h3>
                      <p className="text-sm text-gray-600 mb-3">当主要策略失效时的处理方式</p>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-3">
                          <input
                            type="radio"
                            name="fallbackStrategy"
                            value="auto"
                            checked={config.fallbackStrategy === 'auto'}
                            onChange={(e) => setConfig({ ...config, fallbackStrategy: e.target.value as any })}
                            className="text-blue-600"
                          />
                          <span>自动选择最佳可用链接</span>
                        </label>
                        <label className="flex items-center space-x-3">
                          <input
                            type="radio"
                            name="fallbackStrategy"
                            value="priority"
                            checked={config.fallbackStrategy === 'priority'}
                            onChange={(e) => setConfig({ ...config, fallbackStrategy: e.target.value as any })}
                            className="text-blue-600"
                          />
                          <span>按优先级顺序尝试</span>
                        </label>
                      </div>
                    </div>

                    {/* 接口状态显示 */}
                    <div>
                      <h3 className="font-medium text-gray-900 mb-3">可用接口</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {providers.map(provider => (
                          <div key={provider.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div>
                              <div className="font-medium">{provider.name}</div>
                              <div className="text-sm text-gray-600">{provider.description}</div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {provider.isPremium && (
                                <Badge variant="warning" className="text-xs">高级</Badge>
                              )}
                              <Badge variant="success" className="text-xs">可用</Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* 操作按钮 */}
                <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={onClose}
                    disabled={saving}
                  >
                    取消
                  </Button>
                  <Button
                    onClick={saveConfig}
                    disabled={saving}
                  >
                    {saving ? '保存中...' : '保存配置'}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
