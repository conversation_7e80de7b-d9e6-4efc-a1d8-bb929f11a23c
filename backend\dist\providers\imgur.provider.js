"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImgurProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const base_provider_1 = require("./base.provider");
class ImgurProvider extends base_provider_1.BaseProvider {
    constructor(config) {
        super(config);
    }
    async upload(fileBuffer, fileName, mimeType) {
        const startTime = Date.now();
        try {
            const validation = this.validateFile(fileBuffer, fileName, mimeType);
            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.error || '文件验证失败',
                    providerId: this.config.id,
                    providerName: this.config.name,
                };
            }
            const formData = new form_data_1.default();
            formData.append('image', fileBuffer, {
                filename: fileName,
                contentType: mimeType
            });
            formData.append('type', 'file');
            formData.append('title', fileName);
            const response = await axios_1.default.post('https://api.imgur.com/3/image', formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Authorization': `Client-ID ${this.config.apiKey}`,
                },
                timeout: 30000,
            });
            const responseTime = Date.now() - startTime;
            if (response.data && response.data.success && response.data.data) {
                const data = response.data.data;
                return {
                    success: true,
                    url: data.link,
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                    metadata: {
                        id: data.id,
                        deletehash: data.deletehash,
                        width: data.width,
                        height: data.height,
                        size: data.size,
                    }
                };
            }
            else {
                return {
                    success: false,
                    error: 'Imgur上传失败或响应格式错误',
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                };
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            let errorMessage = 'Imgur上传失败';
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 403) {
                    errorMessage = 'Imgur API密钥无效或已过期';
                }
                else if (error.response?.status === 429) {
                    errorMessage = 'Imgur API请求频率超限';
                }
                else if (error.response?.data?.data?.error) {
                    errorMessage = `Imgur错误: ${error.response.data.data.error}`;
                }
            }
            return {
                success: false,
                error: errorMessage,
                providerId: this.config.id,
                providerName: this.config.name,
                responseTime,
            };
        }
    }
    async healthCheck() {
        const startTime = Date.now();
        try {
            const response = await axios_1.default.get('https://api.imgur.com/3/account/me', {
                headers: {
                    'Authorization': `Client-ID ${this.config.apiKey}`,
                },
                timeout: 10000,
            });
            const responseTime = Date.now() - startTime;
            if (response.data && response.data.success) {
                return {
                    isHealthy: true,
                    responseTime,
                    lastChecked: new Date(),
                };
            }
            else {
                return {
                    isHealthy: false,
                    error: 'Imgur API响应异常',
                    responseTime,
                    lastChecked: new Date(),
                };
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            let errorMessage = 'Imgur健康检查失败';
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 403) {
                    errorMessage = 'Imgur API密钥无效';
                }
                else if (error.response?.status === 429) {
                    errorMessage = 'Imgur API请求频率超限';
                }
            }
            return {
                isHealthy: false,
                error: errorMessage,
                responseTime,
                lastChecked: new Date(),
            };
        }
    }
    async delete(url) {
        try {
            const imageId = this.extractImageIdFromUrl(url);
            if (!imageId) {
                return {
                    success: false,
                    error: '无法从URL中提取图片ID'
                };
            }
            const response = await axios_1.default.delete(`https://api.imgur.com/3/image/${imageId}`, {
                headers: {
                    'Authorization': `Client-ID ${this.config.apiKey}`,
                },
                timeout: 10000,
            });
            if (response.data && response.data.success) {
                return { success: true };
            }
            else {
                return {
                    success: false,
                    error: 'Imgur删除失败'
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Imgur删除失败'
            };
        }
    }
    async getFileInfo(url) {
        try {
            const imageId = this.extractImageIdFromUrl(url);
            if (!imageId) {
                return {
                    success: false,
                    error: '无法从URL中提取图片ID'
                };
            }
            const response = await axios_1.default.get(`https://api.imgur.com/3/image/${imageId}`, {
                headers: {
                    'Authorization': `Client-ID ${this.config.apiKey}`,
                },
                timeout: 10000,
            });
            if (response.data && response.data.success && response.data.data) {
                const data = response.data.data;
                return {
                    success: true,
                    data: {
                        id: data.id,
                        title: data.title,
                        description: data.description,
                        width: data.width,
                        height: data.height,
                        size: data.size,
                        type: data.type,
                        views: data.views,
                        datetime: data.datetime,
                    }
                };
            }
            else {
                return {
                    success: false,
                    error: 'Imgur获取文件信息失败'
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Imgur获取文件信息失败'
            };
        }
    }
    extractImageIdFromUrl(url) {
        const match = url.match(/imgur\.com\/([a-zA-Z0-9]+)/);
        return match && match[1] ? match[1] : null;
    }
}
exports.ImgurProvider = ImgurProvider;
//# sourceMappingURL=imgur.provider.js.map