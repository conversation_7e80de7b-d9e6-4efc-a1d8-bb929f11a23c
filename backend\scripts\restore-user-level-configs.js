/**
 * 恢复用户等级配置脚本
 * 重新创建所有必要的用户等级配置
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 用户等级配置数据
const userLevelConfigs = [
  {
    level: 'free',
    displayName: '免费用户',
    description: '基础功能，有限制的上传额度',
    maxDailyUploads: 50,
    maxFileSize: BigInt(10 * 1024 * 1024), // 10MB
    maxStorageSpace: BigInt(1024 * 1024 * 1024), // 1GB
    allowedFormats: ['image/jpeg', 'image/png', 'image/gif'],
    features: {
      basicUpload: true,
      imageProxy: true,
      linkSharing: true,
      basicAnalytics: false,
      customDomain: false,
      apiAccess: false,
      prioritySupport: false,
      bulkUpload: false,
      advancedSettings: false
    },
    restrictions: {
      dailyUploadLimit: 50,
      fileSizeLimit: '10MB',
      storageLimit: '1GB',
      concurrentUploads: 3,
      retentionDays: 30
    }
  },
  {
    level: 'vip1',
    displayName: 'VIP1用户',
    description: '增强功能，更高的上传额度',
    maxDailyUploads: 200,
    maxFileSize: BigInt(50 * 1024 * 1024), // 50MB
    maxStorageSpace: BigInt(10 * 1024 * 1024 * 1024), // 10GB
    allowedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    features: {
      basicUpload: true,
      imageProxy: true,
      linkSharing: true,
      basicAnalytics: true,
      customDomain: false,
      apiAccess: true,
      prioritySupport: false,
      bulkUpload: true,
      advancedSettings: true
    },
    restrictions: {
      dailyUploadLimit: 200,
      fileSizeLimit: '50MB',
      storageLimit: '10GB',
      concurrentUploads: 5,
      retentionDays: 90
    }
  },
  {
    level: 'vip2',
    displayName: 'VIP2用户',
    description: '高级功能，大容量存储',
    maxDailyUploads: 500,
    maxFileSize: BigInt(100 * 1024 * 1024), // 100MB
    maxStorageSpace: BigInt(50 * 1024 * 1024 * 1024), // 50GB
    allowedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    features: {
      basicUpload: true,
      imageProxy: true,
      linkSharing: true,
      basicAnalytics: true,
      customDomain: true,
      apiAccess: true,
      prioritySupport: true,
      bulkUpload: true,
      advancedSettings: true
    },
    restrictions: {
      dailyUploadLimit: 500,
      fileSizeLimit: '100MB',
      storageLimit: '50GB',
      concurrentUploads: 10,
      retentionDays: 180
    }
  },
  {
    level: 'vip3',
    displayName: 'VIP3用户',
    description: '顶级功能，无限制使用',
    maxDailyUploads: 2000,
    maxFileSize: BigInt(500 * 1024 * 1024), // 500MB
    maxStorageSpace: BigInt(200 * 1024 * 1024 * 1024), // 200GB
    allowedFormats: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/bmp', 'image/tiff'],
    features: {
      basicUpload: true,
      imageProxy: true,
      linkSharing: true,
      basicAnalytics: true,
      customDomain: true,
      apiAccess: true,
      prioritySupport: true,
      bulkUpload: true,
      advancedSettings: true
    },
    restrictions: {
      dailyUploadLimit: 2000,
      fileSizeLimit: '500MB',
      storageLimit: '200GB',
      concurrentUploads: 20,
      retentionDays: 365
    }
  },
  {
    level: 'admin',
    displayName: '管理员',
    description: '系统管理员，拥有所有权限',
    maxDailyUploads: 10000,
    maxFileSize: BigInt(1024 * 1024 * 1024), // 1GB
    maxStorageSpace: BigInt(1024 * 1024 * 1024 * 1024), // 1TB
    allowedFormats: ['*'], // 支持所有格式
    features: {
      basicUpload: true,
      imageProxy: true,
      linkSharing: true,
      basicAnalytics: true,
      customDomain: true,
      apiAccess: true,
      prioritySupport: true,
      bulkUpload: true,
      advancedSettings: true,
      systemManagement: true,
      userManagement: true,
      configManagement: true
    },
    restrictions: {
      dailyUploadLimit: 10000,
      fileSizeLimit: '1GB',
      storageLimit: '1TB',
      concurrentUploads: 50,
      retentionDays: -1 // 永久保存
    }
  }
];

async function restoreUserLevelConfigs() {
  console.log('🔄 开始恢复用户等级配置...\n');

  try {
    // 1. 检查现有配置
    console.log('1️⃣ 检查现有用户等级配置...');
    const existingConfigs = await prisma.userLevelConfig.findMany();
    console.log(`📊 现有配置数量: ${existingConfigs.length}`);

    if (existingConfigs.length > 0) {
      console.log('📋 现有配置:');
      existingConfigs.forEach(config => {
        console.log(`   - ${config.level}: ${config.displayName}`);
      });
    }

    // 2. 删除现有配置（如果需要重置）
    console.log('\n2️⃣ 清理现有配置...');
    const deleteResult = await prisma.userLevelConfig.deleteMany({});
    console.log(`🗑️ 删除了 ${deleteResult.count} 个现有配置`);

    // 3. 创建新的用户等级配置
    console.log('\n3️⃣ 创建新的用户等级配置...');
    
    for (const config of userLevelConfigs) {
      try {
        const createdConfig = await prisma.userLevelConfig.create({
          data: config
        });
        console.log(`✅ 创建成功: ${createdConfig.level} - ${createdConfig.displayName}`);
      } catch (error) {
        console.error(`❌ 创建失败: ${config.level} - ${error.message}`);
      }
    }

    // 4. 验证创建结果
    console.log('\n4️⃣ 验证创建结果...');
    const newConfigs = await prisma.userLevelConfig.findMany({
      orderBy: { level: 'asc' }
    });

    console.log(`📊 新配置数量: ${newConfigs.length}`);
    console.log('📋 配置详情:');
    newConfigs.forEach(config => {
      console.log(`   ✅ ${config.level}: ${config.displayName}`);
      console.log(`      - 每日上传限制: ${config.maxDailyUploads}`);
      console.log(`      - 文件大小限制: ${Number(config.maxFileSize) / (1024 * 1024)}MB`);
      console.log(`      - 存储空间限制: ${Number(config.maxStorageSpace) / (1024 * 1024 * 1024)}GB`);
      console.log(`      - 支持格式: ${config.allowedFormats.join(', ')}`);
    });

    // 5. 更新现有用户的等级（如果需要）
    console.log('\n5️⃣ 检查现有用户...');
    const users = await prisma.user.findMany({
      select: { id: true, username: true, userLevel: true }
    });

    console.log(`👥 现有用户数量: ${users.length}`);
    
    if (users.length > 0) {
      console.log('📋 用户等级分布:');
      const levelCounts = {};
      users.forEach(user => {
        levelCounts[user.userLevel] = (levelCounts[user.userLevel] || 0) + 1;
      });
      
      Object.entries(levelCounts).forEach(([level, count]) => {
        console.log(`   ${level}: ${count} 个用户`);
      });

      // 检查是否有用户使用了不存在的等级
      const validLevels = newConfigs.map(config => config.level);
      const invalidUsers = users.filter(user => !validLevels.includes(user.userLevel));
      
      if (invalidUsers.length > 0) {
        console.log('\n⚠️ 发现使用无效等级的用户:');
        invalidUsers.forEach(user => {
          console.log(`   - ${user.username}: ${user.userLevel}`);
        });
        
        console.log('\n🔄 将无效等级用户设置为free等级...');
        for (const user of invalidUsers) {
          await prisma.user.update({
            where: { id: user.id },
            data: { userLevel: 'free' }
          });
          console.log(`✅ 用户 ${user.username} 已设置为 free 等级`);
        }
      }
    }

    console.log('\n🎉 用户等级配置恢复完成！');

    // 6. 生成配置报告
    console.log('\n📊 配置报告:');
    console.log('=' .repeat(60));
    console.log('等级配置总览:');
    newConfigs.forEach(config => {
      console.log(`\n📋 ${config.level.toUpperCase()} - ${config.displayName}`);
      console.log(`   描述: ${config.description}`);
      console.log(`   每日上传: ${config.maxDailyUploads} 次`);
      console.log(`   文件大小: ${Number(config.maxFileSize) / (1024 * 1024)}MB`);
      console.log(`   存储空间: ${Number(config.maxStorageSpace) / (1024 * 1024 * 1024)}GB`);
      console.log(`   支持格式: ${config.allowedFormats.length} 种`);
      console.log(`   功能特性: ${Object.keys(config.features).filter(key => config.features[key]).length} 项`);
    });
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ 恢复用户等级配置失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 执行脚本
if (require.main === module) {
  restoreUserLevelConfigs()
    .then(() => {
      console.log('\n✨ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { restoreUserLevelConfigs };
