{"version": 3, "file": "qiniu.provider.js", "sourceRoot": "", "sources": ["../../src/providers/qiniu.provider.ts"], "names": [], "mappings": ";;;;;;AAKA,kDAA0B;AAC1B,0DAAiC;AACjC,mDAAgG;AAEhG,MAAa,aAAc,SAAQ,4BAAY;IAC7C,YAAY,MAAsB;QAChC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,QAAQ;oBACnC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;iBAC/B,CAAC;YACJ,CAAC;YAGD,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,IAAI,kEAAkE,CAAC;YAE9H,MAAM,aAAa,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,aAAa,EAAE;gBACnD,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACtF,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,cAAc;oBACrB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;iBAC/B,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YAG5C,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAGpD,MAAM,QAAQ,GAAG,IAAI,mBAAQ,EAAE,CAAC;YAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAClC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAC9B,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAC/B,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAChC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;gBAClC,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,QAAQ;aACtB,CAAC,CAAC;YAGH,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,yBAAyB,CAAC;YACpE,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE;gBACrD,OAAO,EAAE,QAAQ,CAAC,UAAU,EAAE;gBAC9B,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEvC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,qCAAqC,CAAC;gBACrF,MAAM,QAAQ,GAAG,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;gBAE7C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,GAAG,EAAE,QAAQ;oBACb,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBAC9B,YAAY;oBACZ,QAAQ,EAAE;wBACR,GAAG,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG;wBACtB,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;wBACxB,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;qBAC7B;iBACF,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,gBAAgB;oBACvB,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;oBAC9B,YAAY;iBACb,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE;gBACpE,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC9B,YAAY;aACb,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,IAAI,kEAAkE,CAAC;YAE9H,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,aAAa,EAAE;gBAC9C,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACpE,OAAO;oBACL,SAAS,EAAE,IAAI;oBACf,YAAY;oBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO;oBACL,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,eAAe;oBACtB,YAAY;oBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;iBACxB,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;gBACxD,YAAY;gBACZ,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,oBAAoB,CAAC,YAAoB;QAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAGrD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAGrF,MAAM,GAAG,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC;QAGnD,OAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,CAAC;IACpE,CAAC;IAKQ,KAAK,CAAC,MAAM,CAAC,GAAW;QAG/B,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,oBAAoB;SAC5B,CAAC;IACJ,CAAC;IAKQ,KAAK,CAAC,WAAW,CAAC,GAAW;QACpC,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,GAAG,EAAE;gBACrC,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC;oBAC7C,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC;oBACjD,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC;oBAC/C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;iBAC/B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU;aAC3D,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA1MD,sCA0MC"}