/**
 * 水滴云上传接口测试脚本
 * 独立测试水滴云接口的可用性
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 创建一个1x1像素的测试PNG图片
function createTestImage() {
  return Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
    0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
    0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  ]);
}

async function testShuidiUpload() {
  try {
    console.log('🧪 开始测试水滴云上传接口...\n');

    // 创建测试图片
    const testImageBuffer = createTestImage();
    console.log('✅ 测试图片创建成功');

    // 构建FormData
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'test-image.png',
      contentType: 'image/png'
    });

    console.log('📤 发送上传请求到水滴云...');
    console.log('接口地址: https://upload.shuidi.cn/uploadimage');

    const startTime = Date.now();

    // 发送请求
    const response = await axios.post('https://upload.shuidi.cn/uploadimage', formData, {
      headers: {
        ...formData.getHeaders(),
      },
      timeout: 30000, // 30秒超时
    });

    const responseTime = Date.now() - startTime;

    console.log(`⏱️  响应时间: ${responseTime}ms`);
    console.log('📥 响应状态:', response.status);
    console.log('📄 响应数据:', JSON.stringify(response.data, null, 2));

    // 解析响应
    if (response.data && response.data.path) {
      const path = response.data.path;
      const fullUrl = `https://filehuoshan.shuidi.cn/img/${path}/0x0.jpg`;
      
      console.log('\n✅ 上传成功！');
      console.log('📍 提取的路径:', path);
      console.log('🔗 完整URL:', fullUrl);

      // 测试生成的URL是否可访问
      console.log('\n🔍 测试生成的URL可访问性...');
      try {
        const urlTest = await axios.head(fullUrl, { timeout: 10000 });
        console.log('✅ URL可访问，状态码:', urlTest.status);
      } catch (urlError) {
        console.log('⚠️  URL访问测试失败:', urlError.message);
      }

    } else {
      console.log('\n❌ 上传失败：响应中未找到path字段');
      console.log('响应结构:', Object.keys(response.data || {}));
    }

  } catch (error) {
    console.log('\n❌ 测试失败');
    
    if (axios.isAxiosError(error)) {
      if (error.response) {
        console.log('服务器响应错误:');
        console.log('状态码:', error.response.status);
        console.log('状态文本:', error.response.statusText);
        console.log('响应数据:', error.response.data);
      } else if (error.request) {
        console.log('网络请求失败:', error.message);
        console.log('可能的原因: 网络连接问题或接口不可用');
      } else {
        console.log('请求配置错误:', error.message);
      }
    } else {
      console.log('未知错误:', error.message);
    }
  }
}

// 测试接口健康状况
async function testShuidiHealth() {
  try {
    console.log('\n🏥 测试水滴云接口健康状况...');
    
    const response = await axios.get('https://upload.shuidi.cn/', {
      timeout: 10000,
    });
    
    console.log('✅ 接口域名可访问，状态码:', response.status);
  } catch (error) {
    console.log('⚠️  接口域名访问测试失败:', error.message);
  }
}

// 执行测试
async function runTests() {
  console.log('🚀 水滴云接口测试开始\n');
  console.log('=' .repeat(50));
  
  await testShuidiHealth();
  await testShuidiUpload();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 测试完成');
}

runTests();
