{"version": 3, "file": "queue.service.d.ts", "sourceRoot": "", "sources": ["../../src/services/queue.service.ts"], "names": [], "mappings": "AAAA,OAAO,EAAiB,GAAG,EAAE,MAAM,QAAQ,CAAC;AAM5C,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAChC,iBAAiB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACvC,QAAQ,EAAE;QACR,YAAY,EAAE,MAAM,CAAC;QACrB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,IAAI,EAAE,MAAM,CAAC;KACd,CAAC;CACH;AAED,MAAM,WAAW,sBAAsB;IACrC,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE;QACV,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAC7B,QAAQ,CAAC,EAAE,OAAO,CAAC;QACnB,SAAS,CAAC,EAAE,OAAO,CAAC;KACrB,CAAC;CACH;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAC,WAAW,CAAuB;IACjD,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAgC;IACnE,OAAO,CAAC,MAAM,CAAC,YAAY,CAAwB;IACnD,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAiC;WAGxD,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;WA2D3B,YAAY,CAAC,IAAI,EAAE,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;WAa9D,qBAAqB,CAAC,IAAI,EAAE,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;mBAajF,gBAAgB;mBA2GhB,yBAAyB;mBA6CzB,kBAAkB;mBAyBlB,aAAa;mBAkBb,YAAY;IAUjC,OAAO,CAAC,MAAM,CAAC,mBAAmB;WA6BrB,aAAa,IAAI,OAAO,CAAC;QACpC,MAAM,EAAE,GAAG,CAAC;QACZ,eAAe,EAAE,GAAG,CAAC;KACtB,CAAC;WAmBW,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;WAW5B,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;CAWvC"}