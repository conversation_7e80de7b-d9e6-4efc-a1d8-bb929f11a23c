/**
 * TCL第三方接口测试脚本
 * 测试TCL接口的连通性和功能
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// 创建一个测试图片文件
function createTestImage() {
  // 创建一个简单的1x1像素PNG图片的Buffer
  const pngBuffer = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // width: 1
    0x00, 0x00, 0x00, 0x01, // height: 1
    0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0x90, 0x77, 0x53, 0xDE, // CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // image data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  return pngBuffer;
}

async function testTCLInterface() {
  console.log('🧪 开始测试TCL第三方接口...\n');

  try {
    // 1. 测试接口连通性
    console.log('1️⃣ 测试接口连通性...');
    const endpoint = 'https://service2.tcl.com/api.php/Center/uploadQiniu';
    
    try {
      // 先测试接口是否可达
      const healthResponse = await axios.get(endpoint, { timeout: 10000 });
      console.log('✅ 接口可达，状态码:', healthResponse.status);
    } catch (error) {
      if (error.response) {
        console.log('ℹ️ 接口响应状态码:', error.response.status);
        console.log('ℹ️ 这可能是正常的，因为GET请求可能不被支持');
      } else {
        console.log('⚠️ 接口连通性测试失败:', error.message);
      }
    }
    console.log('');

    // 2. 测试文件上传
    console.log('2️⃣ 测试文件上传...');
    
    const testImageBuffer = createTestImage();
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'test-image.png',
      contentType: 'image/png'
    });

    console.log('📤 正在上传测试图片...');
    const uploadResponse = await axios.post(endpoint, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      timeout: 30000,
    });

    console.log('✅ 上传请求成功！');
    console.log('📊 响应状态码:', uploadResponse.status);
    console.log('📋 响应数据:', JSON.stringify(uploadResponse.data, null, 2));

    // 3. 验证响应格式
    console.log('\n3️⃣ 验证响应格式...');
    
    const responseData = uploadResponse.data;
    if (responseData && responseData.data) {
      console.log('✅ 响应包含data字段');
      console.log('🔗 提取的URL:', responseData.data);
      
      // 测试提取的URL是否可访问
      console.log('\n4️⃣ 测试提取的URL可访问性...');
      try {
        const urlTestResponse = await axios.head(responseData.data, { timeout: 10000 });
        console.log('✅ URL可访问，状态码:', urlTestResponse.status);
        console.log('📄 Content-Type:', urlTestResponse.headers['content-type']);
      } catch (urlError) {
        console.log('❌ URL访问失败:', urlError.message);
      }
    } else {
      console.log('❌ 响应格式不符合预期，缺少data字段');
    }

    console.log('\n🎉 TCL接口测试完成！');
    
    // 5. 生成测试报告
    console.log('\n📋 测试报告:');
    console.log('✅ 接口地址:', endpoint);
    console.log('✅ 上传方式: POST multipart/form-data');
    console.log('✅ 文件字段: file');
    console.log('✅ 响应格式: JSON');
    console.log('✅ 数据提取: response.data.data');
    
    if (responseData && responseData.data) {
      console.log('✅ 测试结果: 成功');
      console.log('✅ 返回URL:', responseData.data);
    } else {
      console.log('❌ 测试结果: 响应格式异常');
    }

  } catch (error) {
    console.error('❌ TCL接口测试失败:', error.message);
    
    if (error.response) {
      console.error('📊 响应状态码:', error.response.status);
      console.error('📋 响应数据:', error.response.data);
    }
    
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查网络连接');
    console.log('2. 确认接口地址是否正确');
    console.log('3. 检查接口是否需要特殊的认证或头部');
    console.log('4. 确认文件格式和大小是否符合要求');
  }
}

// 测试通过系统API使用TCL接口
async function testTCLThroughAPI() {
  console.log('\n🔄 测试通过系统API使用TCL接口...\n');

  try {
    // 1. 先注册一个测试用户
    console.log('1️⃣ 注册测试用户...');
    const registerData = {
      username: 'tcltest' + Date.now(),
      email: 'tcltest' + Date.now() + '@example.com',
      password: 'password123'
    };

    const registerResponse = await axios.post('http://localhost:3000/api/auth/register', registerData);
    console.log('✅ 用户注册成功');

    // 2. 登录获取token
    console.log('2️⃣ 用户登录...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      username: registerData.username,
      password: registerData.password
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功，获得token');

    // 3. 通过系统API上传文件，指定使用TCL接口
    console.log('3️⃣ 通过系统API上传文件...');
    
    const testImageBuffer = createTestImage();
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'tcl-test-image.png',
      contentType: 'image/png'
    });
    formData.append('providerId', '1'); // TCL接口的ID

    const uploadResponse = await axios.post('http://localhost:3000/api/upload/single', formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${token}`
      },
      timeout: 30000,
    });

    console.log('✅ 系统API上传成功！');
    console.log('📋 响应数据:', JSON.stringify(uploadResponse.data, null, 2));

    // 4. 测试代理链接
    if (uploadResponse.data.success && uploadResponse.data.data.systemUrl) {
      console.log('\n4️⃣ 测试代理链接...');
      const proxyUrl = 'http://localhost:3000' + uploadResponse.data.data.systemUrl;
      console.log('🔗 代理链接:', proxyUrl);
      
      try {
        const proxyResponse = await axios.get(proxyUrl, { 
          timeout: 10000,
          maxRedirects: 0,
          validateStatus: function (status) {
            return status >= 200 && status < 400; // 允许重定向
          }
        });
        console.log('✅ 代理链接工作正常，状态码:', proxyResponse.status);
      } catch (proxyError) {
        if (proxyError.response && proxyError.response.status >= 300 && proxyError.response.status < 400) {
          console.log('✅ 代理链接正常重定向，状态码:', proxyError.response.status);
          console.log('🔗 重定向到:', proxyError.response.headers.location);
        } else {
          console.log('❌ 代理链接测试失败:', proxyError.message);
        }
      }
    }

    console.log('\n🎉 系统API测试完成！');

  } catch (error) {
    console.error('❌ 系统API测试失败:', error.message);
    if (error.response) {
      console.error('📊 响应状态码:', error.response.status);
      console.error('📋 响应数据:', error.response.data);
    }
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 TCL第三方接口完整测试\n');
  console.log('=' .repeat(50));
  
  await testTCLInterface();
  
  console.log('\n' + '=' .repeat(50));
  
  await testTCLThroughAPI();
  
  console.log('\n' + '=' .repeat(50));
  console.log('✨ 所有测试完成！');
}

// 执行测试
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n🎯 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testTCLInterface, testTCLThroughAPI };
