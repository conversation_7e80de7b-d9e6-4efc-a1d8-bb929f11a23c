import { Request, Response, NextFunction } from 'express';
export declare class ImageRedirectConfigController {
    static getUserImageConfig(req: Request, res: Response, next: NextFunction): Promise<void>;
    static setUserImageConfig(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUserAvailableProviders(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUserGlobalConfig(req: Request, res: Response, next: NextFunction): Promise<void>;
    static saveUserGlobalConfig(req: Request, res: Response, next: NextFunction): Promise<void>;
    private static getAvailableProviders;
    static getBatchUserImageConfigs(req: Request, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=image-redirect-config.controller.d.ts.map