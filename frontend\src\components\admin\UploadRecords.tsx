import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Input } from '../ui/Input';

import { Checkbox } from '../ui/checkbox';
import { ProxyConfigForm } from './ProxyConfigForm';
import {
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  Calendar,
  User,
  FileImage,
  HardDrive,
  TrendingUp,
  RefreshCw,
  Settings,
  Link
} from 'lucide-react';

interface UploadRecord {
  id: number;
  userId: number;
  imageId: number;
  isOriginalUploader: boolean;
  accessCount: number;
  createdAt: string;
  user: {
    id: number;
    username: string;
    email: string;
    userLevel: string;
    status: string;
  };
  image: {
    id: number;
    publicId: string;
    originalName: string;
    fileSize: string;
    mimeType: string;
    width: number;
    height: number;
    systemUrl: string;
    uploadStatus: string;
    isDeleted: boolean;
    createdAt: string;
    links: Array<{
      id: number;
      url: string;
      status: string;
      provider: string;
    }>;
  };
}

interface UploadStatistics {
  overview: {
    totalUploads: number;
    totalUsers: number;
    totalSize: string;
    recentUploads: number;
    period: string;
  };
  uploadsByType: Array<{
    mimeType: string;
    count: number;
  }>;
  uploadsByUser: Array<{
    userId: number;
    count: number;
    user: {
      id: number;
      username: string;
      email: string;
    };
  }>;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export function UploadRecords() {
  const [records, setRecords] = useState<UploadRecord[]>([]);
  const [statistics, setStatistics] = useState<UploadStatistics | null>(null);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [loading, setLoading] = useState(true);
  const [selectedRecords, setSelectedRecords] = useState<number[]>([]);
  
  // 筛选状态
  const [filters, setFilters] = useState({
    userId: '',
    startDate: '',
    endDate: '',
    mimeType: '',
    status: ''
  });

  const [showStatistics, setShowStatistics] = useState(true);
  const [selectedRecord, setSelectedRecord] = useState<UploadRecord | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showProxyConfigModal, setShowProxyConfigModal] = useState(false);
  const [proxyConfigData, setProxyConfigData] = useState<any>(null);
  const [availableProviders, setAvailableProviders] = useState<any[]>([]);

  // 加载上传记录
  const loadUploadRecords = async (page = 1) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      });

      const response = await fetch(`/api/admin/uploads?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setRecords(data.data.uploads);
          setPagination(data.data.pagination);
        }
      }
    } catch (error) {
      console.error('加载上传记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/admin/uploads/statistics', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStatistics(data.data);
        }
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 删除单个记录
  const deleteRecord = async (recordId: number, reason?: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/admin/uploads/${recordId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (response.ok) {
        await loadUploadRecords(pagination.page);
        await loadStatistics();
      }
    } catch (error) {
      console.error('删除记录失败:', error);
    }
  };

  // 批量删除记录
  const batchDeleteRecords = async (reason?: string) => {
    if (selectedRecords.length === 0) return;

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/admin/uploads/batch-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ 
          recordIds: selectedRecords,
          reason 
        })
      });

      if (response.ok) {
        setSelectedRecords([]);
        await loadUploadRecords(pagination.page);
        await loadStatistics();
      }
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: string) => {
    const size = parseInt(bytes);
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取用户等级颜色
  const getUserLevelColor = (level: string) => {
    switch (level) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'vip3': return 'bg-purple-100 text-purple-800';
      case 'vip2': return 'bg-blue-100 text-blue-800';
      case 'vip1': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 查看记录详情
  const viewRecordDetail = (record: UploadRecord) => {
    setSelectedRecord(record);
    setShowDetailModal(true);
  };

  // 关闭详情模态框
  const closeDetailModal = () => {
    setSelectedRecord(null);
    setShowDetailModal(false);
  };

  // 打开代理配置模态框
  const openProxyConfigModal = async (record: UploadRecord) => {
    try {
      const token = localStorage.getItem('auth_token');

      // 获取图片的详细代理配置
      const response = await fetch(`/api/admin/uploads/image/${record.imageId}/proxy-config`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setProxyConfigData(data.data);
          setSelectedRecord(record);
          setShowProxyConfigModal(true);

          // 加载可用提供商
          await loadAvailableProviders();
        }
      }
    } catch (error) {
      console.error('获取代理配置失败:', error);
    }
  };

  // 关闭代理配置模态框
  const closeProxyConfigModal = () => {
    setProxyConfigData(null);
    setSelectedRecord(null);
    setShowProxyConfigModal(false);
  };

  // 加载可用提供商
  const loadAvailableProviders = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/image-redirect-config/providers', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAvailableProviders(data.data.providers || []);
        }
      }
    } catch (error) {
      console.error('加载提供商失败:', error);
    }
  };

  // 更新代理配置
  const updateProxyConfig = async (config: any) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/admin/uploads/image/${selectedRecord?.imageId}/proxy-config`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 刷新配置数据
          await openProxyConfigModal(selectedRecord!);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('更新代理配置失败:', error);
      return false;
    }
  };

  // 更新链接状态
  const updateLinkStatus = async (linkId: number, status: string, reason?: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/admin/uploads/link/${linkId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status, reason })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 刷新配置数据
          await openProxyConfigModal(selectedRecord!);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('更新链接状态失败:', error);
      return false;
    }
  };

  // 获取文件状态颜色
  const getStatusColor = (isDeleted: boolean, uploadStatus: string) => {
    if (isDeleted) return 'bg-red-100 text-red-800';
    if (uploadStatus === 'completed') return 'bg-green-100 text-green-800';
    if (uploadStatus === 'failed') return 'bg-red-100 text-red-800';
    return 'bg-yellow-100 text-yellow-800';
  };

  useEffect(() => {
    loadUploadRecords();
    loadStatistics();
  }, [filters]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showDetailModal) {
        closeDetailModal();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDetailModal]);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">上传记录管理</h1>
          <p className="text-sm text-gray-600">查看和管理所有用户的上传记录</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowStatistics(!showStatistics)}
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            {showStatistics ? '隐藏' : '显示'}统计
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadUploadRecords(pagination.page)}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 统计信息 */}
      {showStatistics && statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <FileImage className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="text-xs text-gray-600">总上传数</p>
                  <p className="text-lg font-semibold">{statistics.overview.totalUploads}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <User className="w-5 h-5 text-green-500" />
                <div>
                  <p className="text-xs text-gray-600">上传用户</p>
                  <p className="text-lg font-semibold">{statistics.overview.totalUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <HardDrive className="w-5 h-5 text-purple-500" />
                <div>
                  <p className="text-xs text-gray-600">总存储</p>
                  <p className="text-lg font-semibold">{formatFileSize(statistics.overview.totalSize)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-orange-500" />
                <div>
                  <p className="text-xs text-gray-600">最近{statistics.overview.period}</p>
                  <p className="text-lg font-semibold">{statistics.overview.recentUploads}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-sm">
            <Filter className="w-4 h-4 mr-2" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Input
              placeholder="用户ID"
              value={filters.userId}
              onChange={(e) => setFilters(prev => ({ ...prev, userId: e.target.value }))}
            />
            <Input
              type="date"
              placeholder="开始日期"
              value={filters.startDate}
              onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
            />
            <Input
              type="date"
              placeholder="结束日期"
              value={filters.endDate}
              onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
            />
            <select
              className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.mimeType}
              onChange={(e) => setFilters(prev => ({ ...prev, mimeType: e.target.value }))}
            >
              <option value="">全部类型</option>
              <option value="image/jpeg">JPEG</option>
              <option value="image/png">PNG</option>
              <option value="image/gif">GIF</option>
              <option value="image/webp">WebP</option>
            </select>
            <select
              className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            >
              <option value="">全部状态</option>
              <option value="active">正常</option>
              <option value="deleted">已删除</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作 */}
      {selectedRecords.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-orange-800">
                已选择 {selectedRecords.length} 条记录
              </span>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedRecords([])}
                >
                  取消选择
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => batchDeleteRecords('批量删除')}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  批量删除
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 上传记录列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>上传记录</span>
            <span className="text-sm font-normal text-gray-500">
              共 {pagination.total} 条记录
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
              <p className="text-sm text-gray-600">加载中...</p>
            </div>
          ) : records.length === 0 ? (
            <div className="text-center py-8">
              <FileImage className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">暂无上传记录</p>
            </div>
          ) : (
            <div className="space-y-4">
              {records.map((record) => (
                <div
                  key={record.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start space-x-4">
                    <Checkbox
                      checked={selectedRecords.includes(record.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRecords(prev => [...prev, record.id]);
                        } else {
                          setSelectedRecords(prev => prev.filter(id => id !== record.id));
                        }
                      }}
                    />
                    
                    <div className="flex-1 space-y-2">
                      {/* 第一行：文件信息 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">{record.image.originalName}</span>
                          <Badge className={getStatusColor(record.image.isDeleted, record.image.uploadStatus)}>
                            {record.image.isDeleted ? '已删除' : record.image.uploadStatus}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewRecordDetail(record)}
                            title="查看详情"
                          >
                            <Eye className="w-4 h-4 text-blue-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openProxyConfigModal(record)}
                            title="代理配置"
                          >
                            <Settings className="w-4 h-4 text-green-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteRecord(record.id, '管理员删除')}
                            title="删除记录"
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                      
                      {/* 第二行：用户信息 */}
                      <div className="flex items-center space-x-4 text-xs text-gray-600">
                        <span>用户: {record.user.username} ({record.user.email})</span>
                        <Badge className={getUserLevelColor(record.user.userLevel)}>
                          {record.user.userLevel}
                        </Badge>
                        <span>大小: {formatFileSize(record.image.fileSize)}</span>
                        <span>类型: {record.image.mimeType}</span>
                        {record.image.width && record.image.height && (
                          <span>尺寸: {record.image.width}×{record.image.height}</span>
                        )}
                      </div>
                      
                      {/* 第三行：时间和访问信息 */}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>上传时间: {formatDate(record.createdAt)}</span>
                        <span>访问次数: {record.accessCount}</span>
                      </div>
                      
                      {/* 第四行：链接信息 */}
                      {record.image.links.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">提供商:</span>
                          {record.image.links.map((link, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {link.provider}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">
            第 {pagination.page} 页，共 {pagination.totalPages} 页
          </span>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasPrev}
              onClick={() => loadUploadRecords(pagination.page - 1)}
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasNext}
              onClick={() => loadUploadRecords(pagination.page + 1)}
            >
              下一页
            </Button>
          </div>
        </div>
      )}

      {/* 详情模态框 */}
      {showDetailModal && selectedRecord && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">上传记录详情</h2>
              <Button variant="ghost" size="sm" onClick={closeDetailModal}>
                ✕
              </Button>
            </div>

            <div className="space-y-6">
              {/* 文件信息 */}
              <div>
                <h3 className="text-lg font-semibold mb-3">文件信息</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">文件名:</span>
                    <p className="mt-1">{selectedRecord.image.originalName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">文件大小:</span>
                    <p className="mt-1">{formatFileSize(selectedRecord.image.fileSize)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">文件类型:</span>
                    <p className="mt-1">{selectedRecord.image.mimeType}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">文件状态:</span>
                    <p className="mt-1">
                      <Badge className={getStatusColor(selectedRecord.image.isDeleted, selectedRecord.image.uploadStatus)}>
                        {selectedRecord.image.isDeleted ? '已删除' : selectedRecord.image.uploadStatus}
                      </Badge>
                    </p>
                  </div>
                  {selectedRecord.image.width && selectedRecord.image.height && (
                    <div>
                      <span className="font-medium text-gray-600">图片尺寸:</span>
                      <p className="mt-1">{selectedRecord.image.width} × {selectedRecord.image.height}</p>
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-gray-600">公开ID:</span>
                    <p className="mt-1 font-mono text-xs">{selectedRecord.image.publicId}</p>
                  </div>
                </div>
              </div>

              {/* 用户信息 */}
              <div>
                <h3 className="text-lg font-semibold mb-3">用户信息</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">用户名:</span>
                    <p className="mt-1">{selectedRecord.user.username}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">邮箱:</span>
                    <p className="mt-1">{selectedRecord.user.email}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">用户等级:</span>
                    <p className="mt-1">
                      <Badge className={getUserLevelColor(selectedRecord.user.userLevel)}>
                        {selectedRecord.user.userLevel}
                      </Badge>
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">用户状态:</span>
                    <p className="mt-1">{selectedRecord.user.status}</p>
                  </div>
                </div>
              </div>

              {/* 上传信息 */}
              <div>
                <h3 className="text-lg font-semibold mb-3">上传信息</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">上传时间:</span>
                    <p className="mt-1">{formatDate(selectedRecord.createdAt)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">访问次数:</span>
                    <p className="mt-1">{selectedRecord.accessCount}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">原始上传者:</span>
                    <p className="mt-1">{selectedRecord.isOriginalUploader ? '是' : '否'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">系统URL:</span>
                    <p className="mt-1 break-all text-xs">
                      <a
                        href={selectedRecord.image.systemUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        {selectedRecord.image.systemUrl}
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* 存储链接 */}
              {selectedRecord.image.links.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">存储链接</h3>
                  <div className="space-y-2">
                    {selectedRecord.image.links.map((link, index) => (
                      <div key={index} className="border rounded p-3">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="outline">{link.provider}</Badge>
                          <Badge className={link.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {link.status}
                          </Badge>
                        </div>
                        <p className="text-xs break-all text-gray-600">
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline"
                          >
                            {link.url}
                          </a>
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button variant="outline" onClick={closeDetailModal}>
                  关闭
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    deleteRecord(selectedRecord.id, '管理员删除');
                    closeDetailModal();
                  }}
                >
                  删除记录
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 代理配置模态框 */}
      {showProxyConfigModal && selectedRecord && proxyConfigData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-5xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">图片代理配置</h2>
              <Button variant="ghost" size="sm" onClick={closeProxyConfigModal}>
                ✕
              </Button>
            </div>

            <div className="space-y-6">
              {/* 图片基本信息 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-3">图片信息</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">文件名:</span>
                    <p className="mt-1">{proxyConfigData.image.originalName}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">文件大小:</span>
                    <p className="mt-1">{formatFileSize(proxyConfigData.image.fileSize)}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">系统URL:</span>
                    <p className="mt-1 break-all text-xs">
                      <a
                        href={proxyConfigData.image.systemUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        {proxyConfigData.image.systemUrl}
                      </a>
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">公开ID:</span>
                    <p className="mt-1 font-mono text-xs">{proxyConfigData.image.publicId}</p>
                  </div>
                </div>
              </div>

              {/* 当前代理配置 */}
              <div>
                <h3 className="text-lg font-semibold mb-3">当前代理配置</h3>
                <div className="bg-blue-50 rounded-lg p-4">
                  {proxyConfigData.proxyConfig ? (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">重定向策略:</span>
                        <p className="mt-1">
                          <Badge className="bg-blue-100 text-blue-800">
                            {proxyConfigData.proxyConfig.redirectStrategy}
                          </Badge>
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">首选提供商:</span>
                        <p className="mt-1">
                          {proxyConfigData.proxyConfig.preferredProvider ?
                            proxyConfigData.proxyConfig.preferredProvider.description :
                            '未指定'
                          }
                        </p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">回退策略:</span>
                        <p className="mt-1">{proxyConfigData.proxyConfig.fallbackStrategy}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">配置状态:</span>
                        <p className="mt-1">
                          <Badge className={proxyConfigData.proxyConfig.isEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {proxyConfigData.proxyConfig.isEnabled ? '启用' : '禁用'}
                          </Badge>
                        </p>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-600">暂无自定义代理配置，使用系统默认设置</p>
                  )}
                </div>
              </div>

              {/* 存储链接管理 */}
              <div>
                <h3 className="text-lg font-semibold mb-3">存储链接管理</h3>
                <div className="space-y-3">
                  {proxyConfigData.links.map((link: any, index: number) => (
                    <div key={link.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Badge variant="outline" className="text-sm">
                            {link.provider.description || link.provider.name}
                          </Badge>
                          <Badge className={
                            link.status === 'active' ? 'bg-green-100 text-green-800' :
                            link.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }>
                            {link.status}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            优先级: {link.provider.priority}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateLinkStatus(link.id,
                              link.status === 'active' ? 'inactive' : 'active',
                              '管理员手动切换'
                            )}
                          >
                            {link.status === 'active' ? '禁用' : '启用'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateLinkStatus(link.id, 'failed', '管理员标记失效')}
                          >
                            标记失效
                          </Button>
                        </div>
                      </div>
                      <div className="text-xs text-gray-600 break-all">
                        <Link className="w-3 h-3 inline mr-1" />
                        <a
                          href={link.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline"
                        >
                          {link.url}
                        </a>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        创建时间: {formatDate(link.createdAt)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 代理配置修改 */}
              <div>
                <h3 className="text-lg font-semibold mb-3">修改代理配置</h3>
                <ProxyConfigForm
                  currentConfig={proxyConfigData.proxyConfig}
                  availableProviders={availableProviders}
                  onSave={updateProxyConfig}
                />
              </div>

              {/* 关联用户信息 */}
              {proxyConfigData.users.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">关联用户</h3>
                  <div className="space-y-2">
                    {proxyConfigData.users.map((user: any, index: number) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 rounded p-3">
                        <div className="flex items-center space-x-3">
                          <span className="font-medium">{user.username}</span>
                          <span className="text-sm text-gray-600">{user.email}</span>
                          <Badge className={getUserLevelColor(user.userLevel)}>
                            {user.userLevel}
                          </Badge>
                          {user.isOriginalUploader && (
                            <Badge className="bg-purple-100 text-purple-800">原始上传者</Badge>
                          )}
                        </div>
                        <span className="text-sm text-gray-500">
                          访问次数: {user.accessCount}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button variant="outline" onClick={closeProxyConfigModal}>
                  关闭
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
