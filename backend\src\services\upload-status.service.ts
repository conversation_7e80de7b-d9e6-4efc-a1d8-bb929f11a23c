/**
 * 上传状态服务
 * 用于跟踪和显示图片的上传状态，包括主接口和备份接口的进度
 */

import { prisma } from '../config/database';

export interface UploadStatusInfo {
  imageId: number;
  publicId: string;
  originalName: string;
  uploadStatus: string;
  systemUrl: string;
  primaryLink?: {
    provider: string;
    url: string;
    status: string;
    responseTime?: number;
  };
  backupLinks: {
    provider: string;
    url: string;
    status: string;
    responseTime?: number;
  }[];
  backupProgress: {
    completed: number;
    total: number;
    percentage: number;
  };
  createdAt: string;
}

export class UploadStatusService {
  /**
   * 获取图片的详细上传状态
   */
  static async getImageUploadStatus(imageId: number, userId: number): Promise<UploadStatusInfo | null> {
    try {
      // 检查用户是否有权限访问该图片
      const userImage = await prisma.userImage.findUnique({
        where: {
          userId_imageId: {
            userId,
            imageId
          }
        }
      });

      if (!userImage) {
        return null;
      }

      // 获取图片基本信息
      const image = await prisma.image.findUnique({
        where: { id: imageId },
        include: {
          imageLinks: {
            include: {
              provider: {
                select: {
                  id: true,
                  name: true,
                  priority: true
                }
              }
            },
            orderBy: {
              provider: {
                priority: 'asc'
              }
            }
          }
        }
      });

      if (!image) {
        return null;
      }

      // 分析链接状态
      const activeLinks = image.imageLinks.filter(link => link.status === 'active');
      const primaryLink = activeLinks.length > 0 ? activeLinks[0] : null; // 优先级最高的作为主链接
      const backupLinks = activeLinks.slice(1); // 其余作为备份链接

      // 获取所有可用的上传接口数量
      const totalProviders = await prisma.uploadProvider.count({
        where: { status: 'active' }
      });

      // 计算备份进度
      const completedBackups = activeLinks.length;
      const backupProgress = {
        completed: completedBackups,
        total: totalProviders,
        percentage: totalProviders > 0 ? Math.round((completedBackups / totalProviders) * 100) : 0
      };

      return {
        imageId: image.id,
        publicId: image.publicId || image.id.toString(),
        originalName: image.originalName,
        uploadStatus: image.uploadStatus,
        systemUrl: image.systemUrl,
        primaryLink: primaryLink ? {
          provider: primaryLink.provider.name,
          url: primaryLink.externalUrl,
          status: primaryLink.status,
          responseTime: primaryLink.responseTime || undefined
        } : undefined,
        backupLinks: backupLinks.map(link => ({
          provider: link.provider.name,
          url: link.externalUrl,
          status: link.status,
          responseTime: link.responseTime || undefined
        })),
        backupProgress,
        createdAt: image.createdAt.toISOString()
      };

    } catch (error) {
      console.error('获取图片上传状态失败:', error);
      return null;
    }
  }

  /**
   * 获取用户的上传历史，包括备份状态
   */
  static async getUserUploadHistory(
    userId: number,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    uploads: UploadStatusInfo[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const offset = (page - 1) * limit;

      // 获取用户的图片
      const [userImages, total] = await Promise.all([
        prisma.userImage.findMany({
          where: { userId },
          include: {
            image: {
              include: {
                imageLinks: {
                  include: {
                    provider: {
                      select: {
                        id: true,
                        name: true,
                        priority: true
                      }
                    }
                  },
                  orderBy: {
                    provider: {
                      priority: 'asc'
                    }
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit
        }),
        prisma.userImage.count({ where: { userId } })
      ]);

      // 获取总的可用接口数量
      const totalProviders = await prisma.uploadProvider.count({
        where: { status: 'active' }
      });

      // 转换为状态信息
      const uploads: UploadStatusInfo[] = userImages.map(userImage => {
        const image = userImage.image;
        const activeLinks = image.imageLinks.filter(link => link.status === 'active');
        const primaryLink = activeLinks.length > 0 ? activeLinks[0] : null;
        const backupLinks = activeLinks.slice(1);

        const completedBackups = activeLinks.length;
        const backupProgress = {
          completed: completedBackups,
          total: totalProviders,
          percentage: totalProviders > 0 ? Math.round((completedBackups / totalProviders) * 100) : 0
        };

        return {
          imageId: image.id,
          publicId: image.publicId || image.id.toString(),
          originalName: image.originalName,
          uploadStatus: image.uploadStatus,
          systemUrl: image.systemUrl,
          primaryLink: primaryLink ? {
            provider: primaryLink.provider.name,
            url: primaryLink.externalUrl,
            status: primaryLink.status,
            responseTime: primaryLink.responseTime || undefined
          } : undefined,
          backupLinks: backupLinks.map(link => ({
            provider: link.provider.name,
            url: link.externalUrl,
            status: link.status,
            responseTime: link.responseTime || undefined
          })),
          backupProgress,
          createdAt: image.createdAt.toISOString()
        };
      });

      return {
        uploads,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('获取用户上传历史失败:', error);
      return {
        uploads: [],
        pagination: { page, limit, total: 0, totalPages: 0 }
      };
    }
  }

  /**
   * 获取图片的备份统计信息
   */
  static async getBackupStats(imageId: number): Promise<{
    totalProviders: number;
    activeLinks: number;
    failedLinks: number;
    pendingUploads: number;
  }> {
    try {
      const [totalProviders, imageLinks] = await Promise.all([
        prisma.uploadProvider.count({ where: { status: 'active' } }),
        prisma.imageLink.findMany({
          where: { imageId },
          select: { status: true }
        })
      ]);

      const activeLinks = imageLinks.filter(link => link.status === 'active').length;
      const failedLinks = imageLinks.filter(link => link.status === 'failed').length;
      const pendingUploads = Math.max(0, totalProviders - imageLinks.length);

      return {
        totalProviders,
        activeLinks,
        failedLinks,
        pendingUploads
      };

    } catch (error) {
      console.error('获取备份统计失败:', error);
      return {
        totalProviders: 0,
        activeLinks: 0,
        failedLinks: 0,
        pendingUploads: 0
      };
    }
  }
}
