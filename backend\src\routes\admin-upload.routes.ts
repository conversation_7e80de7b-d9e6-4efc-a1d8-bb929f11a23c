import { Router } from 'express';
import { AdminUploadController } from '../controllers/admin-upload.controller';
import { authenticateToken, requireAdmin, requestId, recordIP } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { body, param, query } from 'express-validator';

const router = Router();

// 应用中间件
router.use(requestId);
router.use(recordIP);
router.use(authenticateToken); // 需要认证
router.use(requireAdmin);      // 需要管理员权限

/**
 * @route   GET /api/admin/uploads
 * @desc    获取所有用户的上传记录（分页）
 * @access  Admin
 * @query   page - 页码（默认1）
 * @query   limit - 每页数量（默认20，最大100）
 * @query   userId - 筛选用户ID
 * @query   startDate - 开始日期（YYYY-MM-DD）
 * @query   endDate - 结束日期（YYYY-MM-DD）
 * @query   mimeType - 文件类型筛选
 * @query   status - 状态筛选（active/deleted）
 */
router.get('/',
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('userId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('用户ID必须是正整数'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式不正确'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式不正确'),
  query('mimeType')
    .optional()
    .isString()
    .withMessage('文件类型必须是字符串'),
  query('status')
    .optional()
    .isIn(['active', 'deleted'])
    .withMessage('状态必须是 active 或 deleted'),
  validateRequest,
  AdminUploadController.getAllUploadRecords
);

/**
 * @route   GET /api/admin/uploads/statistics
 * @desc    获取上传统计信息
 * @access  Admin
 * @query   days - 统计天数（默认30天）
 */
router.get('/statistics',
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('统计天数必须在1-365之间'),
  validateRequest,
  AdminUploadController.getUploadStatistics
);

/**
 * @route   DELETE /api/admin/uploads/:recordId
 * @desc    删除指定的上传记录
 * @access  Admin
 */
router.delete('/:recordId',
  param('recordId')
    .isInt({ min: 1 })
    .withMessage('记录ID必须是正整数'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('删除原因不能超过500字符'),
  validateRequest,
  AdminUploadController.deleteUploadRecord
);

/**
 * @route   GET /api/admin/uploads/image/:imageId/proxy-config
 * @desc    获取图片的详细代理配置
 * @access  Admin
 */
router.get('/image/:imageId/proxy-config',
  param('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'),
  validateRequest,
  AdminUploadController.getImageProxyConfig
);

/**
 * @route   PUT /api/admin/uploads/image/:imageId/proxy-config
 * @desc    更新图片的代理配置
 * @access  Admin
 */
router.put('/image/:imageId/proxy-config',
  param('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'),
  body('redirectStrategy')
    .optional()
    .isIn(['auto', 'provider', 'priority'])
    .withMessage('重定向策略必须是 auto, provider 或 priority'),
  body('preferredProviderId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('首选提供商ID必须是正整数'),
  body('fallbackStrategy')
    .optional()
    .isIn(['auto', 'priority'])
    .withMessage('回退策略必须是 auto 或 priority'),
  body('isEnabled')
    .optional()
    .isBoolean()
    .withMessage('启用状态必须是布尔值'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('修改原因不能超过500字符'),
  validateRequest,
  AdminUploadController.updateImageProxyConfig
);

/**
 * @route   PUT /api/admin/uploads/link/:linkId/status
 * @desc    更新图片链接状态
 * @access  Admin
 */
router.put('/link/:linkId/status',
  param('linkId')
    .isInt({ min: 1 })
    .withMessage('链接ID必须是正整数'),
  body('status')
    .isIn(['active', 'inactive', 'failed'])
    .withMessage('状态必须是 active, inactive 或 failed'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('修改原因不能超过500字符'),
  validateRequest,
  AdminUploadController.updateImageLinkStatus
);

/**
 * @route   POST /api/admin/uploads/batch-delete
 * @desc    批量删除上传记录
 * @access  Admin
 */
router.post('/batch-delete',
  body('recordIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('记录ID数组长度必须在1-100之间'),
  body('recordIds.*')
    .isInt({ min: 1 })
    .withMessage('每个记录ID必须是正整数'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('删除原因不能超过500字符'),
  validateRequest,
  AdminUploadController.batchDeleteUploadRecords
);

export default router;
