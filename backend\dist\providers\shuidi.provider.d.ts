import { BaseProvider, ProviderConfig, UploadResult, HealthCheckResult } from './base.provider';
export declare class ShuidiProvider extends BaseProvider {
    constructor(config: ProviderConfig);
    upload(fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult>;
    healthCheck(): Promise<HealthCheckResult>;
    getProviderInfo(): {
        name: string;
        description: string;
        features: string[];
        limitations: string[];
    };
}
//# sourceMappingURL=shuidi.provider.d.ts.map