import { Request, Response, NextFunction } from 'express';
import { prisma } from '../config/database';
import { ErrorCodes } from '../types';
import { ApiResponse } from '../types';

export class ImageRedirectConfigController {
  /**
   * 获取用户图片的跳转配置
   */
  static async getUserImageConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);
      const { imageId } = req.params;

      if (!imageId || isNaN(parseInt(imageId))) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的图片ID',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const imageIdInt = parseInt(imageId);

      // 检查用户是否有权限访问该图片
      const userImage = await prisma.userImage.findUnique({
        where: {
          userId_imageId: {
            userId,
            imageId: imageIdInt
          }
        }
      });

      if (!userImage) {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.FORBIDDEN,
            message: '无权限访问该图片',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 获取用户配置
      const config = await prisma.userImageRedirectConfig.findUnique({
        where: {
          userId_imageId: {
            userId,
            imageId: imageIdInt
          }
        },
        include: {
          preferredProvider: {
            select: {
              id: true,
              name: true,
              description: true,
              status: true
            }
          }
        }
      });

      // 获取图片的可用提供商
      const availableProviders = await ImageRedirectConfigController.getAvailableProviders(userId, imageIdInt);

      res.json({
        success: true,
        data: {
          imageId: imageIdInt,
          config: config ? {
            redirectStrategy: config.redirectStrategy,
            preferredProviderId: config.preferredProviderId,
            preferredProvider: config.preferredProvider,
            fallbackStrategy: config.fallbackStrategy,
            isEnabled: config.isEnabled,
            updatedAt: config.updatedAt
          } : {
            redirectStrategy: 'auto',
            preferredProviderId: null,
            preferredProvider: null,
            fallbackStrategy: 'auto',
            isEnabled: true,
            updatedAt: null
          },
          availableProviders
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户图片配置错误:', error);
      next(error);
    }
  }

  /**
   * 设置用户图片的跳转配置
   */
  static async setUserImageConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);
      const { imageId } = req.params;
      const { redirectStrategy, preferredProviderId, fallbackStrategy, isEnabled } = req.body;

      if (!imageId || isNaN(parseInt(imageId))) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的图片ID',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const imageIdInt = parseInt(imageId);

      // 检查用户是否有权限访问该图片
      const userImage = await prisma.userImage.findUnique({
        where: {
          userId_imageId: {
            userId,
            imageId: imageIdInt
          }
        }
      });

      if (!userImage) {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.FORBIDDEN,
            message: '无权限访问该图片',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 验证跳转策略
      const validStrategies = ['auto', 'provider', 'priority'];
      if (redirectStrategy && !validStrategies.includes(redirectStrategy)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的跳转策略',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 如果指定了提供商，验证提供商是否可用
      if (preferredProviderId) {
        const availableProviders = await ImageRedirectConfigController.getAvailableProviders(userId, imageIdInt);
        const isProviderAvailable = availableProviders.some(p => p.id === preferredProviderId);
        
        if (!isProviderAvailable) {
          res.status(400).json({
            success: false,
            error: {
              code: ErrorCodes.INVALID_INPUT,
              message: '指定的提供商不可用',
              timestamp: new Date().toISOString(),
            }
          } as ApiResponse);
          return;
        }
      }

      // 创建或更新配置
      const config = await prisma.userImageRedirectConfig.upsert({
        where: {
          userId_imageId: {
            userId,
            imageId: imageIdInt
          }
        },
        update: {
          redirectStrategy: redirectStrategy || 'auto',
          preferredProviderId: preferredProviderId || null,
          fallbackStrategy: fallbackStrategy || 'auto',
          isEnabled: isEnabled !== undefined ? isEnabled : true,
        },
        create: {
          userId,
          imageId: imageIdInt,
          redirectStrategy: redirectStrategy || 'auto',
          preferredProviderId: preferredProviderId || null,
          fallbackStrategy: fallbackStrategy || 'auto',
          isEnabled: isEnabled !== undefined ? isEnabled : true,
        },
        include: {
          preferredProvider: {
            select: {
              id: true,
              name: true,
              description: true,
              status: true
            }
          }
        }
      });

      res.json({
        success: true,
        message: '跳转配置已更新',
        data: {
          imageId: imageIdInt,
          config: {
            redirectStrategy: config.redirectStrategy,
            preferredProviderId: config.preferredProviderId,
            preferredProvider: config.preferredProvider,
            fallbackStrategy: config.fallbackStrategy,
            isEnabled: config.isEnabled,
            updatedAt: config.updatedAt
          }
        }
      } as ApiResponse);

    } catch (error) {
      console.error('设置用户图片配置错误:', error);
      next(error);
    }
  }

  /**
   * 获取用户可用的提供商列表
   */
  static async getUserAvailableProviders(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { userLevel: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const providers = await ImageRedirectConfigController.getAvailableProviders(userId);

      res.json({
        success: true,
        data: {
          userLevel: user.userLevel,
          providers
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取可用提供商错误:', error);
      next(error);
    }
  }

  /**
   * 获取用户全局跳转配置
   */
  static async getUserGlobalConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '用户未登录',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 查找用户的全局跳转配置
      const userConfig = await prisma.userRedirectConfig.findUnique({
        where: { userId }
      });

      // 如果没有配置，返回默认配置
      const config = userConfig || {
        redirectMode: 'auto',
        preferredProviderId: null,
        isActive: true
      };

      res.json({
        success: true,
        data: {
          redirectMode: config.redirectMode,
          preferredProviderId: config.preferredProviderId,
          isActive: config.isActive,
          hasCustomConfig: !!userConfig
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户全局配置失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户全局配置失败',
          timestamp: new Date().toISOString(),
        }
      } as ApiResponse);
    }
  }

  /**
   * 保存用户全局跳转配置
   */
  static async saveUserGlobalConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '用户未登录',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const { redirectMode, preferredProviderId, isActive } = req.body;

      // 验证输入参数
      if (!redirectMode || !['auto', 'manual'].includes(redirectMode)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '跳转模式必须是 auto 或 manual',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 如果是手动模式，验证提供商ID
      if (redirectMode === 'manual' && preferredProviderId) {
        const provider = await prisma.uploadProvider.findUnique({
          where: { id: preferredProviderId }
        });

        if (!provider || provider.status !== 'active') {
          res.status(400).json({
            success: false,
            error: {
              code: ErrorCodes.INVALID_INPUT,
              message: '指定的提供商不存在或不可用',
              timestamp: new Date().toISOString(),
            }
          } as ApiResponse);
          return;
        }
      }

      // 保存或更新用户配置
      const savedConfig = await prisma.userRedirectConfig.upsert({
        where: { userId },
        update: {
          redirectMode,
          preferredProviderId: redirectMode === 'manual' ? preferredProviderId : null,
          isActive: isActive !== undefined ? isActive : true,
          updatedAt: new Date()
        },
        create: {
          userId,
          redirectMode,
          preferredProviderId: redirectMode === 'manual' ? preferredProviderId : null,
          isActive: isActive !== undefined ? isActive : true
        }
      });

      res.json({
        success: true,
        message: '跳转配置保存成功',
        data: {
          redirectMode: savedConfig.redirectMode,
          preferredProviderId: savedConfig.preferredProviderId,
          isActive: savedConfig.isActive,
          updatedAt: savedConfig.updatedAt.toISOString()
        }
      } as ApiResponse);

    } catch (error) {
      console.error('保存用户全局配置失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '保存用户全局配置失败',
          timestamp: new Date().toISOString(),
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取可用提供商（内部方法）
   */
  private static async getAvailableProviders(userId: number, imageId?: number): Promise<any[]> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { userLevel: true }
    });

    if (!user) return [];

    // 获取用户等级可见的提供商
    const levelVisibility = await prisma.levelProviderVisibility.findMany({
      where: {
        level: user.userLevel,
        isVisible: true
      },
      include: {
        provider: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            isPremium: true,
            priority: true
          }
        }
      }
    });

    let availableProviders = levelVisibility.map(lv => lv.provider);

    // 如果指定了图片ID，只返回该图片有链接的提供商
    if (imageId) {
      const imageLinks = await prisma.imageLink.findMany({
        where: { imageId },
        select: { providerId: true }
      });

      const linkedProviderIds = imageLinks.map(link => link.providerId);
      availableProviders = availableProviders.filter(provider => 
        linkedProviderIds.includes(provider.id)
      );
    }

    return availableProviders.filter(provider => provider.status === 'active');
  }

  /**
   * 批量获取用户图片的跳转配置
   */
  static async getBatchUserImageConfigs(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);
      const { imageIds } = req.body;

      if (!Array.isArray(imageIds) || imageIds.length === 0) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '请提供有效的图片ID数组',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const imageIdInts = imageIds.map(id => parseInt(id)).filter(id => !isNaN(id));

      // 获取用户有权限的图片
      const userImages = await prisma.userImage.findMany({
        where: {
          userId,
          imageId: { in: imageIdInts }
        },
        select: { imageId: true }
      });

      const accessibleImageIds = userImages.map(ui => ui.imageId);

      // 获取配置
      const configs = await prisma.userImageRedirectConfig.findMany({
        where: {
          userId,
          imageId: { in: accessibleImageIds }
        },
        include: {
          preferredProvider: {
            select: {
              id: true,
              name: true,
              description: true,
              status: true
            }
          }
        }
      });

      const configMap = new Map(configs.map(config => [config.imageId, config]));
      const results = accessibleImageIds.map(imageId => ({
        imageId,
        config: configMap.get(imageId) || {
          redirectStrategy: 'auto',
          preferredProviderId: null,
          preferredProvider: null,
          fallbackStrategy: 'auto',
          isEnabled: true,
          updatedAt: null
        }
      }));

      res.json({
        success: true,
        data: results
      } as ApiResponse);

    } catch (error) {
      console.error('批量获取用户图片配置错误:', error);
      next(error);
    }
  }
}
