/**
 * 测试管理端上传记录API
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testAdminUploadAPI() {
  console.log('🧪 开始测试管理端上传记录API...\n');

  try {
    // 1. 管理员登录
    console.log('1️⃣ 管理员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('管理员登录失败: ' + loginResponse.data.error?.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 管理员登录成功');

    // 2. 测试获取上传统计信息
    console.log('\n2️⃣ 测试获取上传统计信息...');
    const statsResponse = await axios.get(`${BASE_URL}/api/admin/uploads/statistics`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (statsResponse.data.success) {
      console.log('✅ 获取统计信息成功');
      console.log('📊 统计数据:');
      const stats = statsResponse.data.data;
      console.log(`   总上传数: ${stats.overview.totalUploads}`);
      console.log(`   上传用户: ${stats.overview.totalUsers}`);
      console.log(`   总存储: ${formatFileSize(stats.overview.totalSize)}`);
      console.log(`   最近${stats.overview.period}: ${stats.overview.recentUploads}`);
      
      if (stats.uploadsByType.length > 0) {
        console.log('   文件类型分布:');
        stats.uploadsByType.forEach(type => {
          console.log(`     ${type.mimeType}: ${type.count}`);
        });
      }
    } else {
      console.log('❌ 获取统计信息失败:', statsResponse.data.error?.message);
    }

    // 3. 测试获取上传记录列表
    console.log('\n3️⃣ 测试获取上传记录列表...');
    const recordsResponse = await axios.get(`${BASE_URL}/api/admin/uploads?page=1&limit=5`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (recordsResponse.data.success) {
      console.log('✅ 获取上传记录成功');
      const data = recordsResponse.data.data;
      console.log(`📋 记录数据 (第${data.pagination.page}页，共${data.pagination.total}条):`);
      
      data.uploads.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.image.originalName}`);
        console.log(`      用户: ${record.user.username} (${record.user.userLevel})`);
        console.log(`      大小: ${formatFileSize(record.image.fileSize)}`);
        console.log(`      类型: ${record.image.mimeType}`);
        console.log(`      状态: ${record.image.isDeleted ? '已删除' : record.image.uploadStatus}`);
        console.log(`      上传时间: ${new Date(record.createdAt).toLocaleString('zh-CN')}`);
        console.log(`      访问次数: ${record.accessCount}`);
        if (record.image.links.length > 0) {
          console.log(`      提供商: ${record.image.links.map(l => l.provider).join(', ')}`);
        }
        console.log('');
      });
    } else {
      console.log('❌ 获取上传记录失败:', recordsResponse.data.error?.message);
    }

    // 4. 测试筛选功能
    console.log('\n4️⃣ 测试筛选功能...');
    
    // 按文件类型筛选
    const filterResponse = await axios.get(`${BASE_URL}/api/admin/uploads?mimeType=image&limit=3`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (filterResponse.data.success) {
      console.log('✅ 按文件类型筛选成功');
      console.log(`📋 筛选结果: ${filterResponse.data.data.uploads.length} 条图片记录`);
    } else {
      console.log('❌ 筛选失败:', filterResponse.data.error?.message);
    }

    // 按状态筛选
    const statusFilterResponse = await axios.get(`${BASE_URL}/api/admin/uploads?status=active&limit=3`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (statusFilterResponse.data.success) {
      console.log('✅ 按状态筛选成功');
      console.log(`📋 活跃文件: ${statusFilterResponse.data.data.uploads.length} 条记录`);
    } else {
      console.log('❌ 状态筛选失败:', statusFilterResponse.data.error?.message);
    }

    // 5. 测试权限验证
    console.log('\n5️⃣ 测试权限验证...');
    
    // 测试无token访问
    try {
      await axios.get(`${BASE_URL}/api/admin/uploads`);
      console.log('❌ 应该拒绝无token访问');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 正确拒绝了无token访问');
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }

    // 测试无效token访问
    try {
      await axios.get(`${BASE_URL}/api/admin/uploads`, {
        headers: { 'Authorization': 'Bearer invalid_token' }
      });
      console.log('❌ 应该拒绝无效token访问');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 正确拒绝了无效token访问');
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }

    // 6. 测试分页功能
    console.log('\n6️⃣ 测试分页功能...');
    
    const page1Response = await axios.get(`${BASE_URL}/api/admin/uploads?page=1&limit=2`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (page1Response.data.success) {
      const pagination = page1Response.data.data.pagination;
      console.log('✅ 分页功能正常');
      console.log(`📄 分页信息:`);
      console.log(`   当前页: ${pagination.page}`);
      console.log(`   每页数量: ${pagination.limit}`);
      console.log(`   总记录数: ${pagination.total}`);
      console.log(`   总页数: ${pagination.totalPages}`);
      console.log(`   有下一页: ${pagination.hasNext}`);
      console.log(`   有上一页: ${pagination.hasPrev}`);
    } else {
      console.log('❌ 分页测试失败:', page1Response.data.error?.message);
    }

    // 7. 测试参数验证
    console.log('\n7️⃣ 测试参数验证...');
    
    // 测试无效页码
    try {
      await axios.get(`${BASE_URL}/api/admin/uploads?page=0`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ 应该拒绝无效页码');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 正确拒绝了无效页码');
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }

    // 测试过大的limit
    try {
      await axios.get(`${BASE_URL}/api/admin/uploads?limit=200`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ 应该拒绝过大的limit');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ 正确拒绝了过大的limit');
      } else {
        console.log('❌ 意外的错误:', error.message);
      }
    }

    console.log('\n🎉 管理端上传记录API测试完成！');

    // 8. 生成测试报告
    console.log('\n📊 测试报告:');
    console.log('=' .repeat(50));
    console.log('✅ 管理员认证正常');
    console.log('✅ 获取统计信息API正常');
    console.log('✅ 获取上传记录API正常');
    console.log('✅ 筛选功能正常');
    console.log('✅ 分页功能正常');
    console.log('✅ 权限验证正常');
    console.log('✅ 参数验证正常');
    console.log('=' .repeat(50));

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('📊 响应状态:', error.response.status);
      console.error('📋 响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  const size = parseInt(bytes);
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
}

// 运行测试
if (require.main === module) {
  testAdminUploadAPI()
    .then(() => {
      console.log('\n✨ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testAdminUploadAPI };
