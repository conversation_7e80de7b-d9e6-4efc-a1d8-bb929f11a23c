"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShuidiProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const base_provider_1 = require("./base.provider");
class ShuidiProvider extends base_provider_1.BaseProvider {
    constructor(config) {
        super(config);
    }
    async upload(fileBuffer, fileName, mimeType) {
        const startTime = Date.now();
        try {
            const validation = this.validateFile(fileBuffer, fileName, mimeType);
            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.error || '文件验证失败',
                    providerId: this.config.id,
                    providerName: this.config.name,
                };
            }
            const formData = new form_data_1.default();
            formData.append('file', fileBuffer, {
                filename: fileName,
                contentType: mimeType
            });
            const headers = {
                ...formData.getHeaders(),
            };
            if (this.config.apiKey) {
                headers['Authorization'] = `Bearer ${this.config.apiKey}`;
            }
            const response = await axios_1.default.post(this.config.endpoint, formData, {
                headers,
                timeout: 30000,
                maxContentLength: this.config.maxFileSize,
                maxBodyLength: this.config.maxFileSize,
            });
            const responseTime = Date.now() - startTime;
            if (response.data && response.data.path) {
                const fullUrl = `https://filehuoshan.shuidi.cn/img/${response.data.path}/0x0.jpg`;
                return {
                    success: true,
                    url: fullUrl,
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                    metadata: {
                        originalPath: response.data.path,
                        rawResponse: response.data
                    }
                };
            }
            else {
                return {
                    success: false,
                    error: '水滴云接口响应中未找到path字段',
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                };
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            if (axios_1.default.isAxiosError(error)) {
                let errorMessage = '水滴云上传失败';
                if (error.response) {
                    errorMessage = `水滴云服务器错误 (${error.response.status}): ${error.response.statusText}`;
                }
                else if (error.request) {
                    errorMessage = '水滴云服务器无响应，请检查网络连接';
                }
                else {
                    errorMessage = `请求配置错误: ${error.message}`;
                }
                return {
                    success: false,
                    error: errorMessage,
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                };
            }
            return {
                success: false,
                error: `水滴云上传异常: ${error instanceof Error ? error.message : '未知错误'}`,
                providerId: this.config.id,
                providerName: this.config.name,
                responseTime,
            };
        }
    }
    async healthCheck() {
        const startTime = Date.now();
        try {
            const testImageBuffer = Buffer.from([
                0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
                0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
                0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
                0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
                0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            ]);
            const result = await this.upload(testImageBuffer, 'health-check.png', 'image/png');
            const responseTime = Date.now() - startTime;
            return {
                isHealthy: result.success,
                responseTime,
                error: result.success ? undefined : result.error,
                lastChecked: new Date()
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                isHealthy: false,
                responseTime,
                error: error instanceof Error ? error.message : '健康检查失败',
                lastChecked: new Date()
            };
        }
    }
    getProviderInfo() {
        return {
            name: '水滴云',
            description: '水滴云图片上传服务',
            features: [
                '支持常见图片格式',
                '自动生成CDN链接',
                '高可用性保障'
            ],
            limitations: [
                '仅支持图片文件',
                '单文件大小限制',
                '需要稳定网络连接'
            ]
        };
    }
}
exports.ShuidiProvider = ShuidiProvider;
//# sourceMappingURL=shuidi.provider.js.map