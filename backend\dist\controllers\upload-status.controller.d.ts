import { Request, Response, NextFunction } from 'express';
export declare class UploadStatusController {
    static getImageStatus(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getUploadHistory(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getBackupStats(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getProvidersOverview(req: Request, res: Response, next: NextFunction): Promise<void>;
}
//# sourceMappingURL=upload-status.controller.d.ts.map