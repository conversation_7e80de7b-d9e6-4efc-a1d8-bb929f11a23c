import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { <PERSON>, CardHeader, CardT<PERSON><PERSON>, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { uploadStatus } from '../../lib/api';
import { formatFileSize, formatDate, copyToClipboard } from '../../lib/utils';

interface UploadStatusInfo {
  imageId: number;
  publicId: string;
  originalName: string;
  uploadStatus: string;
  systemUrl: string;
  primaryLink?: {
    provider: string;
    url: string;
    status: string;
    responseTime?: number;
  };
  backupLinks: {
    provider: string;
    url: string;
    status: string;
    responseTime?: number;
  }[];
  backupProgress: {
    completed: number;
    total: number;
    percentage: number;
  };
  createdAt: string;
}

interface UploadHistoryData {
  uploads: UploadStatusInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export function EnhancedUploadHistory() {
  const [history, setHistory] = useState<UploadHistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  useEffect(() => {
    loadHistory();
  }, [currentPage]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const response = await uploadStatus.getHistory(currentPage, 20);

      if (response.success && response.data) {
        setHistory(response.data);
      }
    } catch (error) {
      console.error('加载上传历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyUrl = async (url: string, type: string) => {
    try {
      await copyToClipboard(url);
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (error) {
      console.error('复制链接失败:', error);
    }
  };

  const toggleExpanded = (imageId: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(imageId)) {
      newExpanded.delete(imageId);
    } else {
      newExpanded.add(imageId);
    }
    setExpandedItems(newExpanded);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">已完成</Badge>;
      case 'processing':
        return <Badge variant="warning">处理中</Badge>;
      case 'failed':
        return <Badge variant="destructive">失败</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-orange-500';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>上传历史</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="loading-spinner w-6 h-6 mx-auto mb-2" />
          <p className="text-gray-600">加载中...</p>
        </CardContent>
      </Card>
    );
  }

  if (!history || history.uploads.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>上传历史</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-500">暂无上传记录</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>上传历史</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {history.uploads.map((item: UploadStatusInfo) => {
              const isExpanded = expandedItems.has(item.imageId);
              
              return (
                <div key={item.imageId} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {item.originalName}
                        </h3>
                        {getStatusBadge(item.uploadStatus)}
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <span>{formatDate(item.createdAt)}</span>
                        {item.primaryLink && (
                          <span>主接口: {item.primaryLink.provider}</span>
                        )}
                      </div>

                      {/* 备份进度条 */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                          <span>备份进度</span>
                          <span>{item.backupProgress.completed}/{item.backupProgress.total} ({item.backupProgress.percentage}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(item.backupProgress.percentage)}`}
                            style={{ width: `${item.backupProgress.percentage}%` }}
                          />
                        </div>
                      </div>

                      {/* 主链接 */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between bg-blue-50 p-2 rounded">
                          <div className="flex items-center space-x-2">
                            <span className="text-xs font-medium text-blue-700">系统链接</span>
                            <Badge variant="default" className="text-xs">永久有效</Badge>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCopyUrl(item.systemUrl, 'system')}
                            className="text-xs"
                          >
                            {copiedUrl === item.systemUrl ? '已复制' : '复制链接'}
                          </Button>
                        </div>

                        {/* 展开/收起按钮 */}
                        {item.backupLinks.length > 0 && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleExpanded(item.imageId)}
                            className="text-xs text-gray-600 hover:text-gray-800"
                          >
                            {isExpanded ? '收起' : '查看'} {item.backupLinks.length} 个备份链接
                            <svg 
                              className={`w-4 h-4 ml-1 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                              fill="none" 
                              stroke="currentColor" 
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </Button>
                        )}

                        {/* 备份链接列表 */}
                        {isExpanded && item.backupLinks.length > 0 && (
                          <div className="space-y-1 pl-4 border-l-2 border-gray-200">
                            {item.backupLinks.map((link, index) => (
                              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded text-xs">
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium">{link.provider}</span>
                                  <Badge variant="secondary" className="text-xs">备份</Badge>
                                  {link.responseTime && (
                                    <span className="text-gray-500">{link.responseTime}ms</span>
                                  )}
                                </div>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleCopyUrl(link.url, 'backup')}
                                  className="text-xs"
                                >
                                  {copiedUrl === link.url ? '已复制' : '复制'}
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* 分页 */}
          {history.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="text-sm text-gray-600">
                第 {history.pagination.page} 页，共 {history.pagination.totalPages} 页
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(history.pagination.totalPages, prev + 1))}
                  disabled={currentPage === history.pagination.totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
