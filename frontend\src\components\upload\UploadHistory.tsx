import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { upload } from '../../lib/api';
import { formatFileSize, formatDate, copyToClipboard } from '../../lib/utils';

interface UploadHistoryType {
  uploads: UploadItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UploadItem {
  id: number;
  originalName: string;
  fileSize: number;
  mimeType: string;
  systemUrl: string;
  uploadStatus: string;
  isOriginalUploader: boolean;
  accessCount: number;
  createdAt: string;
  links: UploadLink[];
}

interface UploadLink {
  provider: string;
  url: string;
  status: string;
}

export function UploadHistory() {
  const [history, setHistory] = useState<UploadHistoryType | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);

  useEffect(() => {
    loadHistory();
  }, [currentPage]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const response = await upload.getHistory(currentPage, 20);

      if (response.success && response.data) {
        setHistory(response.data);
      }
    } catch (error) {
      console.error('加载上传历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyUrl = async (url: string) => {
    try {
      await copyToClipboard(url);
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const handleDelete = async (imageId: number) => {
    if (!confirm('确定要删除这张图片吗？')) {
      return;
    }

    try {
      const response = await upload.delete(imageId);
      if (response.success) {
        loadHistory(); // 重新加载列表
      }
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  const handleRetry = async (imageId: number) => {
    try {
      const response = await upload.retry(imageId);
      if (response.success) {
        loadHistory(); // 重新加载列表
      }
    } catch (error) {
      console.error('重试失败:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">上传成功</Badge>;
      case 'processing':
        return <Badge variant="warning">处理中</Badge>;
      case 'failed':
        return <Badge variant="error">上传失败</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
          <p className="text-gray-600">加载中...</p>
        </CardContent>
      </Card>
    );
  }

  if (!history || history.uploads.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>上传历史</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-500">暂无上传记录</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>上传历史</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {history.uploads.map((item: UploadItem) => (
              <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {item.originalName}
                      </h3>
                      {getStatusBadge(item.uploadStatus)}
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                      <span>{formatFileSize(item.fileSize)}</span>
                      <span>{formatDate(item.createdAt)}</span>
                      <span>访问 {item.accessCount} 次</span>
                    </div>

                    {/* 代理链接 */}
                    <div className="space-y-2">
                      <p className="text-xs font-medium text-gray-700">永久代理链接:</p>
                      <div className="flex items-center justify-between bg-blue-50 rounded p-2 border border-blue-200">
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
                            代理链接
                          </Badge>
                          <span className="text-xs text-blue-700 truncate font-mono">
                            {item.systemUrl}
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyUrl(item.systemUrl)}
                          className="ml-2 text-blue-600 hover:text-blue-700 hover:bg-blue-100"
                        >
                          {copiedUrl === item.systemUrl ? '已复制' : '复制'}
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500">
                        💡 这是永久有效的代理链接，即使原始链接失效也能正常访问
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {item.uploadStatus === 'failed' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRetry(item.id)}
                      >
                        重试
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(item.id)}
                      className="text-error-600 hover:text-error-700"
                    >
                      删除
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 分页 */}
          {history.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="text-sm text-gray-500">
                第 {history.pagination.page} 页，共 {history.pagination.totalPages} 页
                （总计 {history.pagination.total} 条记录）
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(history.pagination.totalPages, prev + 1))}
                  disabled={currentPage === history.pagination.totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
