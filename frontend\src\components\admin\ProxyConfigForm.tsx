import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

interface ProxyConfigFormProps {
  currentConfig: any;
  availableProviders: any[];
  onSave: (config: any) => Promise<boolean>;
}

export function ProxyConfigForm({ currentConfig, availableProviders, onSave }: ProxyConfigFormProps) {
  const [formData, setFormData] = useState({
    redirectStrategy: 'auto',
    preferredProviderId: null as number | null,
    fallbackStrategy: 'auto',
    isEnabled: true,
    reason: ''
  });

  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (currentConfig) {
      setFormData({
        redirectStrategy: currentConfig.redirectStrategy || 'auto',
        preferredProviderId: currentConfig.preferredProviderId || null,
        fallbackStrategy: currentConfig.fallbackStrategy || 'auto',
        isEnabled: currentConfig.isEnabled !== false,
        reason: ''
      });
    }
  }, [currentConfig]);

  const handleSave = async () => {
    setSaving(true);
    try {
      const success = await onSave(formData);
      if (success) {
        setFormData(prev => ({ ...prev, reason: '' }));
      }
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4 space-y-4">
      {/* 重定向策略 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          重定向策略
        </label>
        <select
          className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={formData.redirectStrategy}
          onChange={(e) => setFormData(prev => ({ 
            ...prev, 
            redirectStrategy: e.target.value,
            preferredProviderId: e.target.value !== 'provider' ? null : prev.preferredProviderId
          }))}
        >
          <option value="auto">自动最优</option>
          <option value="provider">指定提供商</option>
          <option value="priority">按优先级</option>
        </select>
        <p className="text-xs text-gray-500 mt-1">
          {formData.redirectStrategy === 'auto' && '系统自动选择最佳链接'}
          {formData.redirectStrategy === 'provider' && '优先使用指定的提供商'}
          {formData.redirectStrategy === 'priority' && '按提供商优先级顺序选择'}
        </p>
      </div>

      {/* 首选提供商 */}
      {formData.redirectStrategy === 'provider' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            首选提供商
          </label>
          <select
            className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={formData.preferredProviderId || ''}
            onChange={(e) => setFormData(prev => ({ 
              ...prev, 
              preferredProviderId: e.target.value ? parseInt(e.target.value) : null
            }))}
          >
            <option value="">请选择提供商</option>
            {availableProviders.map(provider => (
              <option key={provider.id} value={provider.id}>
                {provider.displayName || provider.name}
                {provider.isPremium && ' (高级)'}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* 回退策略 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          回退策略
        </label>
        <select
          className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={formData.fallbackStrategy}
          onChange={(e) => setFormData(prev => ({ ...prev, fallbackStrategy: e.target.value }))}
        >
          <option value="auto">自动选择</option>
          <option value="priority">按优先级</option>
        </select>
        <p className="text-xs text-gray-500 mt-1">
          当首选方案失效时的处理策略
        </p>
      </div>

      {/* 启用状态 */}
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="isEnabled"
          checked={formData.isEnabled}
          onChange={(e) => setFormData(prev => ({ ...prev, isEnabled: e.target.checked }))}
          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="isEnabled" className="text-sm font-medium text-gray-700">
          启用自定义代理配置
        </label>
      </div>

      {/* 修改原因 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          修改原因
        </label>
        <textarea
          className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
          placeholder="请输入修改原因..."
          value={formData.reason}
          onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
        />
      </div>

      {/* 当前配置预览 */}
      {currentConfig && (
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">配置变更预览</h4>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-gray-500">当前策略:</span>
              <Badge className="ml-2 bg-gray-100 text-gray-800">
                {currentConfig.redirectStrategy || 'auto'}
              </Badge>
            </div>
            <div>
              <span className="text-gray-500">新策略:</span>
              <Badge className="ml-2 bg-blue-100 text-blue-800">
                {formData.redirectStrategy}
              </Badge>
            </div>
            <div>
              <span className="text-gray-500">当前状态:</span>
              <Badge className={`ml-2 ${currentConfig.isEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {currentConfig.isEnabled ? '启用' : '禁用'}
              </Badge>
            </div>
            <div>
              <span className="text-gray-500">新状态:</span>
              <Badge className={`ml-2 ${formData.isEnabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {formData.isEnabled ? '启用' : '禁用'}
              </Badge>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-2 pt-4 border-t">
        <Button
          variant="outline"
          onClick={() => {
            if (currentConfig) {
              setFormData({
                redirectStrategy: currentConfig.redirectStrategy || 'auto',
                preferredProviderId: currentConfig.preferredProviderId || null,
                fallbackStrategy: currentConfig.fallbackStrategy || 'auto',
                isEnabled: currentConfig.isEnabled !== false,
                reason: ''
              });
            }
          }}
        >
          重置
        </Button>
        <Button
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? '保存中...' : '保存配置'}
        </Button>
      </div>

      {/* 帮助信息 */}
      <div className="bg-blue-50 border border-blue-200 rounded p-3">
        <h5 className="text-sm font-medium text-blue-900 mb-1">💡 配置说明</h5>
        <ul className="text-xs text-blue-800 space-y-1">
          <li><strong>自动最优:</strong> 系统根据响应时间和成功率自动选择最佳链接</li>
          <li><strong>指定提供商:</strong> 优先使用指定的提供商，失效时自动切换</li>
          <li><strong>按优先级:</strong> 严格按照提供商优先级顺序选择链接</li>
          <li><strong>禁用配置:</strong> 将使用系统默认的全局配置</li>
        </ul>
      </div>
    </div>
  );
}
