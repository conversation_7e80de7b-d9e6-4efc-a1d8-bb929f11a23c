{"version": 3, "file": "link-selection.service.js", "sourceRoot": "", "sources": ["../../src/services/link-selection.service.ts"], "names": [], "mappings": ";;;AAAA,iDAA4C;AAiB5C,MAAa,oBAAoB;IAI/B,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,MAAe;QAC1D,IAAI,CAAC;YAEH,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,MAAM,EAAE,CAAC;gBACX,UAAU,GAAG,MAAM,iBAAM,CAAC,uBAAuB,CAAC,UAAU,CAAC;oBAC3D,KAAK,EAAE;wBACL,cAAc,EAAE;4BACd,MAAM;4BACN,OAAO;yBACR;qBACF;oBACD,OAAO,EAAE;wBACP,iBAAiB,EAAE,IAAI;qBACxB;iBACF,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;gBACtB,OAAO,EAAE;oBACP,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,QAAQ,EAAE,IAAI;yBACf;wBACD,KAAK,EAAE;4BACL,MAAM,EAAE,QAAQ;yBACjB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjE,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;gBACvC,OAAO,MAAM,oBAAoB,CAAC,oBAAoB,CACpD,KAAK,CAAC,UAAU,EAChB,UAAU,EACV,MAAM,CACP,CAAC;YACJ,CAAC;YAGD,MAAM,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACtD,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAClE,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACvF,OAAO,CAAC,GAAG,CAAC,wBAAwB,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,SAAS,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC;gBACjH,OAAO,oBAAoB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,CAAC;YAGD,OAAO,MAAM,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAEnF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACvC,KAAY,EACZ,UAAe,EACf,MAAe;QAEf,QAAQ,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACpC,KAAK,UAAU;gBAEb,IAAI,UAAU,CAAC,mBAAmB,EAAE,CAAC;oBACnC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACtC,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,mBAAmB,CACnD,CAAC;oBACF,IAAI,aAAa,EAAE,CAAC;wBAClB,OAAO,oBAAoB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;gBAED,IAAI,UAAU,CAAC,gBAAgB,KAAK,MAAM,EAAE,CAAC;oBAC3C,OAAO,MAAM,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACxE,CAAC;gBACD,MAAM;YAER,KAAK,UAAU;gBAEb,OAAO,MAAM,oBAAoB,CAAC,wBAAwB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE5E,KAAK,MAAM,CAAC;YACZ;gBAEE,OAAO,MAAM,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACvC,KAAY,EACZ,MAAe;QAEf,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,oBAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAGjF,IAAI,SAAS,GAAG,MAAM,CAAC;QACvB,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;aAC5B,CAAC,CAAC;YACH,SAAS,GAAG,IAAI,EAAE,SAAS,IAAI,MAAM,CAAC;QACxC,CAAC;QAGD,MAAM,eAAe,GAAG,MAAM,oBAAoB,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAE3F,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAEjC,OAAO,oBAAoB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,oBAAoB,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAGnF,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACnD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC5C,CAAC;QAEF,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/E,OAAO,YAAY,CAAC,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrF,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC3C,KAAY,EACZ,MAAe;QAGf,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAEtC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,OAAO,oBAAoB,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAY,EAAE,SAAiB;QACxE,MAAM,eAAe,GAAG,EAAE,CAAC;QAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YAEzB,MAAM,eAAe,GAAG,MAAM,iBAAM,CAAC,uBAAuB,CAAC,UAAU,CAAC;gBACtE,KAAK,EAAE;oBACL,gBAAgB,EAAE;wBAChB,KAAK,EAAE,SAAS;wBAChB,UAAU,EAAE,IAAI,CAAC,UAAU;qBAC5B;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,eAAe,EAAE,SAAS,EAAE,CAAC;gBAC/B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAY;QACnD,MAAM,MAAM,GAAgB,EAAE,CAAC;QAE/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,KAAK,GAAG,GAAG,CAAC;YAChB,IAAI,OAAO,GAAa,EAAE,CAAC;YAG3B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;gBAClE,KAAK,IAAI,aAAa,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,QAAQ,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9E,CAAC;YAGD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YAC1C,KAAK,IAAI,YAAY,CAAC;YACtB,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,MAAM,YAAY,GAAG,CAAC,CAAC;YAC9D,CAAC;YAGD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;YACvE,KAAK,IAAI,aAAa,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,EAAE,MAAM,aAAa,GAAG,CAAC,CAAC;YAGzE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,MAAM,eAAe,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC/F,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,eAAe,CAAC,CAAC;gBACzD,KAAK,IAAI,cAAc,CAAC;gBACxB,OAAO,CAAC,IAAI,CAAC,SAAS,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACxF,CAAC;YAED,MAAM,CAAC,IAAI,CAAC;gBACV,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;gBACzB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,MAAM,CAAC,kBAAkB,CAAC,IAAS;QACzC,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAChC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAe;QAC7C,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE,EAAE,OAAO,EAAE;YAClB,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;qBACb;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,KAAK,CAAC,MAAM;YACnB,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM;YACvD,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM;YACvD,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,MAAM;YAC3D,eAAe,EAAE,CAAC;YAClB,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACzB,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACjB,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI;gBACrB,MAAM,EAAE,CAAC,CAAC,MAAM;gBAChB,YAAY,EAAE,CAAC,CAAC,YAAY;gBAC5B,UAAU,EAAE,CAAC,CAAC,UAAU;aACzB,CAAC,CAAC;SACJ,CAAC;QAEF,MAAM,aAAa,GAAG,KAAK;aACxB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,IAAI,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAa,CAAC,CAAC;QAE7B,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAChC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAC1E,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AArSD,oDAqSC"}