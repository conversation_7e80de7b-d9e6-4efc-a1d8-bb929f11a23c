import { prisma } from '../config/database';

export interface SelectedLink {
  id: number;
  externalUrl: string;
  providerId: number;
  providerName: string;
  responseTime?: number;
  status: string;
}

export interface LinkScore {
  linkId: number;
  score: number;
  reason: string;
}

export class LinkSelectionService {
  /**
   * 选择最佳链接 - 核心算法
   */
  static async selectBestLink(imageId: number, userId?: number): Promise<SelectedLink | null> {
    try {
      // 1. 获取用户的跳转配置（如果有用户ID）
      let userConfig = null;
      if (userId) {
        userConfig = await prisma.userImageRedirectConfig.findUnique({
          where: {
            userId_imageId: {
              userId,
              imageId
            }
          },
          include: {
            preferredProvider: true
          }
        });
      }

      // 2. 获取图片的所有可用链接
      const image = await prisma.image.findUnique({
        where: { id: imageId },
        include: {
          imageLinks: {
            include: {
              provider: true
            },
            where: {
              status: 'active' // 只获取活跃链接
            }
          }
        }
      });

      if (!image || !image.imageLinks || image.imageLinks.length === 0) {
        return null;
      }

      // 3. 根据用户配置选择策略
      if (userConfig && userConfig.isEnabled) {
        return await LinkSelectionService.selectByUserStrategy(
          image.imageLinks,
          userConfig,
          userId
        );
      }

      // 4. 没有用户配置时，优先选择最早创建的链接（用户上传时选择的主接口）
      const sortedByCreation = image.imageLinks.sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      if (sortedByCreation.length > 0 && sortedByCreation[0] && sortedByCreation[0].provider) {
        console.log(`🎯 没有用户配置，选择最早创建的链接: ${sortedByCreation[0].provider.name} (ID: ${sortedByCreation[0].providerId})`);
        return LinkSelectionService.formatSelectedLink(sortedByCreation[0]);
      }

      // 5. 备用方案：使用默认的自动选择策略
      return await LinkSelectionService.selectByAutoStrategy(image.imageLinks, userId);

    } catch (error) {
      console.error('链接选择服务错误:', error);
      return null;
    }
  }

  /**
   * 根据用户策略选择链接
   */
  private static async selectByUserStrategy(
    links: any[],
    userConfig: any,
    userId?: number
  ): Promise<SelectedLink | null> {
    switch (userConfig.redirectStrategy) {
      case 'provider':
        // 用户指定提供商
        if (userConfig.preferredProviderId) {
          const preferredLink = links.find(link => 
            link.providerId === userConfig.preferredProviderId
          );
          if (preferredLink) {
            return LinkSelectionService.formatSelectedLink(preferredLink);
          }
        }
        // 如果指定提供商不可用，根据备用策略处理
        if (userConfig.fallbackStrategy === 'auto') {
          return await LinkSelectionService.selectByAutoStrategy(links, userId);
        }
        break;

      case 'priority':
        // 按优先级选择（基于用户等级和提供商优先级）
        return await LinkSelectionService.selectByPriorityStrategy(links, userId);

      case 'auto':
      default:
        // 自动最优选择
        return await LinkSelectionService.selectByAutoStrategy(links, userId);
    }

    return null;
  }

  /**
   * 自动最优选择算法
   */
  private static async selectByAutoStrategy(
    links: any[],
    userId?: number
  ): Promise<SelectedLink | null> {
    if (links.length === 0) return null;
    if (links.length === 1) return LinkSelectionService.formatSelectedLink(links[0]);

    // 获取用户等级信息（用于权限过滤）
    let userLevel = 'free';
    if (userId) {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { userLevel: true }
      });
      userLevel = user?.userLevel || 'free';
    }

    // 过滤用户可访问的链接
    const accessibleLinks = await LinkSelectionService.filterAccessibleLinks(links, userLevel);
    
    if (accessibleLinks.length === 0) {
      // 如果没有可访问的链接，返回第一个可用链接
      return LinkSelectionService.formatSelectedLink(links[0]);
    }

    // 计算每个链接的得分
    const linkScores = await LinkSelectionService.calculateLinkScores(accessibleLinks);
    
    // 选择得分最高的链接
    const bestLink = linkScores.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    const selectedLink = accessibleLinks.find(link => link.id === bestLink.linkId);
    return selectedLink ? LinkSelectionService.formatSelectedLink(selectedLink) : null;
  }

  /**
   * 按优先级策略选择
   */
  private static async selectByPriorityStrategy(
    links: any[],
    userId?: number
  ): Promise<SelectedLink | null> {
    // 按提供商优先级排序
    const sortedLinks = links.sort((a, b) => {
      // 优先级高的排前面（数值小的优先级高）
      return (a.provider.priority || 999) - (b.provider.priority || 999);
    });

    return LinkSelectionService.formatSelectedLink(sortedLinks[0]);
  }

  /**
   * 过滤用户可访问的链接
   */
  private static async filterAccessibleLinks(links: any[], userLevel: string): Promise<any[]> {
    const accessibleLinks = [];

    for (const link of links) {
      // 检查等级可见性
      const levelVisibility = await prisma.levelProviderVisibility.findUnique({
        where: {
          level_providerId: {
            level: userLevel,
            providerId: link.providerId
          }
        }
      });

      if (levelVisibility?.isVisible) {
        accessibleLinks.push(link);
      }
    }

    return accessibleLinks;
  }

  /**
   * 计算链接得分
   */
  private static async calculateLinkScores(links: any[]): Promise<LinkScore[]> {
    const scores: LinkScore[] = [];

    for (const link of links) {
      let score = 100; // 基础分数
      let reasons: string[] = [];

      // 响应时间得分（响应时间越短得分越高）
      if (link.responseTime) {
        const responseScore = Math.max(0, 50 - (link.responseTime / 100));
        score += responseScore;
        reasons.push(`响应时间: ${link.responseTime}ms (+${responseScore.toFixed(1)})`);
      }

      // 错误率得分（错误越少得分越高）
      const errorPenalty = link.errorCount * 10;
      score -= errorPenalty;
      if (errorPenalty > 0) {
        reasons.push(`错误次数: ${link.errorCount} (-${errorPenalty})`);
      }

      // 提供商优先级得分
      const priorityScore = Math.max(0, 20 - (link.provider.priority || 10));
      score += priorityScore;
      reasons.push(`优先级: ${link.provider.priority || 10} (+${priorityScore})`);

      // 最近检查时间得分
      if (link.lastChecked) {
        const hoursSinceCheck = (Date.now() - new Date(link.lastChecked).getTime()) / (1000 * 60 * 60);
        const freshnessScore = Math.max(0, 10 - hoursSinceCheck);
        score += freshnessScore;
        reasons.push(`检查时效: ${hoursSinceCheck.toFixed(1)}h前 (+${freshnessScore.toFixed(1)})`);
      }

      scores.push({
        linkId: link.id,
        score: Math.max(0, score),
        reason: reasons.join(', ')
      });
    }

    return scores;
  }

  /**
   * 格式化选中的链接
   */
  private static formatSelectedLink(link: any): SelectedLink {
    return {
      id: link.id,
      externalUrl: link.externalUrl,
      providerId: link.providerId,
      providerName: link.provider.name,
      responseTime: link.responseTime,
      status: link.status
    };
  }

  /**
   * 获取链接健康状态统计
   */
  static async getLinkHealthStats(imageId: number): Promise<any> {
    const links = await prisma.imageLink.findMany({
      where: { imageId },
      include: {
        provider: {
          select: {
            id: true,
            name: true,
            status: true
          }
        }
      }
    });

    const stats = {
      total: links.length,
      active: links.filter(l => l.status === 'active').length,
      failed: links.filter(l => l.status === 'failed').length,
      checking: links.filter(l => l.status === 'checking').length,
      avgResponseTime: 0,
      providers: links.map(l => ({
        id: l.provider.id,
        name: l.provider.name,
        status: l.status,
        responseTime: l.responseTime,
        errorCount: l.errorCount
      }))
    };

    const responseTimes = links
      .filter(l => l.responseTime !== null)
      .map(l => l.responseTime!);
    
    if (responseTimes.length > 0) {
      stats.avgResponseTime = Math.round(
        responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      );
    }

    return stats;
  }
}
