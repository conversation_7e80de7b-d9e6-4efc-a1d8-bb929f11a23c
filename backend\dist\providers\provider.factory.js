"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderFactory = void 0;
const qiniu_provider_1 = require("./qiniu.provider");
const imgur_provider_1 = require("./imgur.provider");
class ProviderFactory {
    static createProvider(config) {
        const providerName = config.name.toLowerCase();
        switch (providerName) {
            case 'qiniu':
            case '七牛云':
                return new qiniu_provider_1.QiniuProvider(config);
            case 'imgur':
                return new imgur_provider_1.ImgurProvider(config);
            default:
                throw new Error(`不支持的接口类型: ${config.name}`);
        }
    }
    static getSupportedProviders() {
        return [
            'qiniu',
            '七牛云',
            'imgur',
        ];
    }
    static isProviderSupported(providerName) {
        return this.getSupportedProviders().includes(providerName.toLowerCase());
    }
    static createProviders(configs) {
        return configs.map(config => this.createProvider(config));
    }
    static async createProviderWithHealthCheck(config) {
        try {
            const provider = this.createProvider(config);
            const healthResult = await provider.healthCheck();
            return {
                provider,
                isHealthy: healthResult.isHealthy,
                ...(healthResult.error && { error: healthResult.error }),
            };
        }
        catch (error) {
            return {
                provider: this.createProvider(config),
                isHealthy: false,
                error: error instanceof Error ? error.message : '创建接口适配器失败',
            };
        }
    }
}
exports.ProviderFactory = ProviderFactory;
//# sourceMappingURL=provider.factory.js.map