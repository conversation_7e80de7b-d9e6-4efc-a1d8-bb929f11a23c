{"version": 3, "file": "link-health-check.service.js", "sourceRoot": "", "sources": ["../../src/services/link-health-check.service.ts"], "names": [], "mappings": ";;;;;;AAAA,iDAA4C;AAC5C,kDAA0B;AAU1B,MAAa,sBAAsB;IASjC,MAAM,CAAC,wBAAwB;QAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAG7B,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;QAG5C,sBAAsB,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YACnD,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;QAC9C,CAAC,EAAE,sBAAsB,CAAC,cAAc,CAAC,CAAC;IAC5C,CAAC;IAKD,MAAM,CAAC,uBAAuB;QAC5B,IAAI,sBAAsB,CAAC,UAAU,EAAE,CAAC;YACtC,aAAa,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YACjD,sBAAsB,CAAC,UAAU,GAAG,IAAI,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAGhC,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAC5C,KAAK,EAAE;oBACL,EAAE,EAAE;wBACF,EAAE,WAAW,EAAE,IAAI,EAAE;wBACrB;4BACE,WAAW,EAAE;gCACX,EAAE,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,sBAAsB,CAAC,cAAc,CAAC;6BACjE;yBACF;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,QAAQ,EAAE;wBACR,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE,IAAI;4BACV,MAAM,EAAE,IAAI;yBACb;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,EAAE,WAAW,EAAE,KAAK,EAAE;oBACtB,EAAE,UAAU,EAAE,MAAM,EAAE;iBACvB;gBACD,IAAI,EAAE,GAAG;aACV,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC3B,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,SAAS,KAAK,CAAC,MAAM,UAAU,CAAC,CAAC;YAG7C,MAAM,OAAO,GAAG,sBAAsB,CAAC,UAAU,CAAC,KAAK,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;YACvG,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CACtC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAChE,CAAC;gBAGF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC1B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBAEtB,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI;wBAAE,SAAS;oBAE/B,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;wBAClC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;wBACjC,MAAM,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;wBAEpE,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;4BAC1B,YAAY,EAAE,CAAC;wBACjB,CAAC;6BAAM,CAAC;4BACN,cAAc,EAAE,CAAC;wBACnB,CAAC;oBACH,CAAC;yBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;wBACxC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;wBAEvD,MAAM,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE;4BACrD,MAAM,EAAE,IAAI,CAAC,EAAE;4BACf,SAAS,EAAE,KAAK;4BAChB,KAAK,EAAE,WAAW;yBACnB,CAAC,CAAC;wBACH,cAAc,EAAE,CAAC;oBACnB,CAAC;oBAED,YAAY,EAAE,CAAC;gBACjB,CAAC;gBAGD,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChD,MAAM,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,YAAY,WAAW,YAAY,SAAS,cAAc,IAAI,CAAC,CAAC;QAE9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,IAAS;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBAClD,OAAO,EAAE,sBAAsB,CAAC,OAAO;gBACvC,cAAc,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,GAAG,GAAG;gBACxC,YAAY,EAAE,CAAC;gBACf,OAAO,EAAE;oBACP,YAAY,EAAE,4BAA4B;iBAC3C;aACF,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;YAElE,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS;gBACT,YAAY;gBACZ,UAAU,EAAE,QAAQ,CAAC,MAAM;aAC5B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS,EAAE,KAAK;gBAChB,YAAY;gBACZ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;aAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,MAAyB;QAC7E,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBACpD,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,WAAW;gBAAE,OAAO;YAEzB,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;YACzD,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YAE1E,MAAM,iBAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACrB,IAAI,EAAE;oBACJ,MAAM,EAAE,SAAS;oBACjB,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,IAAI;oBACzC,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,UAAU,EAAE,aAAa;iBAC1B;aACF,CAAC,CAAC;YAGH,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;gBACvB,OAAO,CAAC,IAAI,CAAC,SAAS,MAAM,SAAS,aAAa,aAAa,CAAC,CAAC;YACnE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,QAAQ,MAAM,WAAW,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,OAAe;QAC1C,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE,EAAE,OAAO,EAAE;YAClB,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,IAAI;qBACb;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG,MAAM,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAClE,MAAM,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAC/D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,cAAc;QACzB,MAAM,KAAK,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,OAAO,CAAC;YAC3C,EAAE,EAAE,CAAC,QAAQ,CAAC;YACd,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;aACT;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,KAAK,CAAC;YACnD,KAAK,EAAE;gBACL,WAAW,EAAE;oBACX,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;iBAChD;aACF;SACF,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,SAAS,CAAC;YACvD,IAAI,EAAE;gBACJ,YAAY,EAAE,IAAI;aACnB;YACD,KAAK,EAAE;gBACL,YAAY,EAAE;oBACZ,GAAG,EAAE,IAAI;iBACV;gBACD,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE,UAAU;YACjB,eAAe;YACf,kBAAkB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAC7C,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAA4B,CAAC;YAChC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;YACvE,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACxC,CAAC;IACJ,CAAC;IAKO,MAAM,CAAC,UAAU,CAAI,KAAU,EAAE,SAAiB;QACxD,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,MAAM,CAAC,KAAK,CAAC,EAAU;QAC7B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;;AAjSH,wDAkSC;AAjSyB,8BAAO,GAAG,KAAK,CAAC;AAChB,4CAAqB,GAAG,CAAC,CAAC;AAC1B,qCAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACzC,iCAAU,GAA0B,IAAI,CAAC"}