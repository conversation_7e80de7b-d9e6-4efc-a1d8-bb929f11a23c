
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  passwordHash: 'passwordHash',
  role: 'role',
  userLevel: 'userLevel',
  status: 'status',
  avatarUrl: 'avatarUrl',
  levelExpiresAt: 'levelExpiresAt',
  displayName: 'displayName',
  bio: 'bio',
  location: 'location',
  website: 'website',
  profileVisibility: 'profileVisibility',
  allowDirectMessages: 'allowDirectMessages',
  showOnlineStatus: 'showOnlineStatus',
  emailNotifications: 'emailNotifications',
  pushNotifications: 'pushNotifications',
  marketingEmails: 'marketingEmails',
  defaultImageQuality: 'defaultImageQuality',
  autoCompress: 'autoCompress',
  defaultImageFormat: 'defaultImageFormat',
  maxImageSize: 'maxImageSize',
  twoFactorEnabled: 'twoFactorEnabled',
  twoFactorSecret: 'twoFactorSecret',
  lastPasswordChange: 'lastPasswordChange',
  loginNotifications: 'loginNotifications',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt',
  lastActiveAt: 'lastActiveAt'
};

exports.Prisma.UploadProviderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  endpoint: 'endpoint',
  apiKey: 'apiKey',
  config: 'config',
  status: 'status',
  priority: 'priority',
  maxFileSize: 'maxFileSize',
  supportedFormats: 'supportedFormats',
  requiredLevel: 'requiredLevel',
  isPremium: 'isPremium',
  costPerUpload: 'costPerUpload',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ImageScalarFieldEnum = {
  id: 'id',
  publicId: 'publicId',
  originalName: 'originalName',
  fileHash: 'fileHash',
  fileMd5: 'fileMd5',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  width: 'width',
  height: 'height',
  systemUrl: 'systemUrl',
  uploadStatus: 'uploadStatus',
  isDeleted: 'isDeleted',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ImageLinkScalarFieldEnum = {
  id: 'id',
  imageId: 'imageId',
  providerId: 'providerId',
  externalUrl: 'externalUrl',
  status: 'status',
  responseTime: 'responseTime',
  lastChecked: 'lastChecked',
  errorCount: 'errorCount',
  createdAt: 'createdAt'
};

exports.Prisma.AccessLogScalarFieldEnum = {
  id: 'id',
  imageId: 'imageId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  referer: 'referer',
  country: 'country',
  city: 'city',
  providerUsed: 'providerUsed',
  responseTime: 'responseTime',
  createdAt: 'createdAt'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  tokenHash: 'tokenHash',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.UserLevelConfigScalarFieldEnum = {
  id: 'id',
  level: 'level',
  displayName: 'displayName',
  maxDailyUploads: 'maxDailyUploads',
  maxFileSize: 'maxFileSize',
  maxStorageSpace: 'maxStorageSpace',
  canChooseProvider: 'canChooseProvider',
  prioritySupport: 'prioritySupport',
  customDomain: 'customDomain',
  visibleProviderCount: 'visibleProviderCount',
  createdAt: 'createdAt'
};

exports.Prisma.LevelProviderVisibilityScalarFieldEnum = {
  id: 'id',
  level: 'level',
  providerId: 'providerId',
  isVisible: 'isVisible',
  displayOrder: 'displayOrder',
  createdAt: 'createdAt'
};

exports.Prisma.UserProviderPermissionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  providerId: 'providerId',
  grantedBy: 'grantedBy',
  grantedAt: 'grantedAt',
  expiresAt: 'expiresAt',
  isActive: 'isActive'
};

exports.Prisma.UserLevelHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  oldLevel: 'oldLevel',
  newLevel: 'newLevel',
  changedBy: 'changedBy',
  reason: 'reason',
  createdAt: 'createdAt'
};

exports.Prisma.UserImageScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  imageId: 'imageId',
  isOriginalUploader: 'isOriginalUploader',
  accessCount: 'accessCount',
  createdAt: 'createdAt'
};

exports.Prisma.UserImageRedirectConfigScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  imageId: 'imageId',
  redirectStrategy: 'redirectStrategy',
  preferredProviderId: 'preferredProviderId',
  fallbackStrategy: 'fallbackStrategy',
  isEnabled: 'isEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserRedirectConfigScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  redirectMode: 'redirectMode',
  preferredProviderId: 'preferredProviderId',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserIpHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  ipAddress: 'ipAddress',
  country: 'country',
  city: 'city',
  isp: 'isp',
  isTrusted: 'isTrusted',
  firstSeen: 'firstSeen',
  lastSeen: 'lastSeen',
  loginCount: 'loginCount',
  isBlocked: 'isBlocked',
  createdAt: 'createdAt'
};

exports.Prisma.IpRiskRuleScalarFieldEnum = {
  id: 'id',
  ruleName: 'ruleName',
  ruleType: 'ruleType',
  timeWindow: 'timeWindow',
  maxAttempts: 'maxAttempts',
  blockDuration: 'blockDuration',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IpRiskLogScalarFieldEnum = {
  id: 'id',
  ipAddress: 'ipAddress',
  userId: 'userId',
  ruleId: 'ruleId',
  actionType: 'actionType',
  riskScore: 'riskScore',
  isBlocked: 'isBlocked',
  blockExpiresAt: 'blockExpiresAt',
  userAgent: 'userAgent',
  referer: 'referer',
  createdAt: 'createdAt'
};

exports.Prisma.SecurityConfigScalarFieldEnum = {
  id: 'id',
  configKey: 'configKey',
  configValue: 'configValue',
  description: 'description',
  isActive: 'isActive',
  updatedBy: 'updatedBy',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemMetricScalarFieldEnum = {
  id: 'id',
  metricType: 'metricType',
  metricValue: 'metricValue',
  unit: 'unit',
  serverInstance: 'serverInstance',
  createdAt: 'createdAt'
};

exports.Prisma.UploadLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  imageId: 'imageId',
  actionType: 'actionType',
  fileName: 'fileName',
  fileSize: 'fileSize',
  fileHash: 'fileHash',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  uploadDuration: 'uploadDuration',
  providerUsed: 'providerUsed',
  errorMessage: 'errorMessage',
  isSuccess: 'isSuccess',
  createdAt: 'createdAt'
};

exports.Prisma.AdminOperationLogScalarFieldEnum = {
  id: 'id',
  adminId: 'adminId',
  operationType: 'operationType',
  targetType: 'targetType',
  targetId: 'targetId',
  operationDetails: 'operationDetails',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.SystemEventLogScalarFieldEnum = {
  id: 'id',
  eventType: 'eventType',
  eventLevel: 'eventLevel',
  eventMessage: 'eventMessage',
  eventData: 'eventData',
  serverInstance: 'serverInstance',
  createdAt: 'createdAt'
};

exports.Prisma.SystemConfigScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  theme: 'theme',
  language: 'language',
  timezone: 'timezone',
  uploadSettings: 'uploadSettings',
  privacySettings: 'privacySettings',
  notificationSettings: 'notificationSettings',
  securitySettings: 'securitySettings',
  customSettings: 'customSettings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  activityType: 'activityType',
  activityData: 'activityData',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  location: 'location',
  deviceInfo: 'deviceInfo',
  createdAt: 'createdAt'
};

exports.Prisma.UserLoginHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  loginType: 'loginType',
  isSuccess: 'isSuccess',
  failureReason: 'failureReason',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  location: 'location',
  deviceInfo: 'deviceInfo',
  sessionId: 'sessionId',
  createdAt: 'createdAt'
};

exports.Prisma.UserLevelApplicationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  currentLevel: 'currentLevel',
  requestedLevel: 'requestedLevel',
  reason: 'reason',
  contactInfo: 'contactInfo',
  businessInfo: 'businessInfo',
  expectedUsage: 'expectedUsage',
  additionalInfo: 'additionalInfo',
  status: 'status',
  adminId: 'adminId',
  adminComment: 'adminComment',
  processedAt: 'processedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvitationCodeScalarFieldEnum = {
  id: 'id',
  code: 'code',
  isUsed: 'isUsed',
  usedBy: 'usedBy',
  usedAt: 'usedAt',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt',
  description: 'description'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  User: 'User',
  UploadProvider: 'UploadProvider',
  Image: 'Image',
  ImageLink: 'ImageLink',
  AccessLog: 'AccessLog',
  UserSession: 'UserSession',
  UserLevelConfig: 'UserLevelConfig',
  LevelProviderVisibility: 'LevelProviderVisibility',
  UserProviderPermission: 'UserProviderPermission',
  UserLevelHistory: 'UserLevelHistory',
  UserImage: 'UserImage',
  UserImageRedirectConfig: 'UserImageRedirectConfig',
  UserRedirectConfig: 'UserRedirectConfig',
  UserIpHistory: 'UserIpHistory',
  IpRiskRule: 'IpRiskRule',
  IpRiskLog: 'IpRiskLog',
  SecurityConfig: 'SecurityConfig',
  SystemMetric: 'SystemMetric',
  UploadLog: 'UploadLog',
  AdminOperationLog: 'AdminOperationLog',
  SystemEventLog: 'SystemEventLog',
  SystemConfig: 'SystemConfig',
  UserSettings: 'UserSettings',
  UserActivityLog: 'UserActivityLog',
  UserLoginHistory: 'UserLoginHistory',
  UserLevelApplication: 'UserLevelApplication',
  InvitationCode: 'InvitationCode'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
