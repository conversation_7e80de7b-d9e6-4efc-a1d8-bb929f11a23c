"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProviderController = void 0;
const user_provider_service_1 = require("../services/user-provider.service");
class UserProviderController {
    static async getAvailableProviders(req, res) {
        try {
            const userId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_PARAMS',
                        message: '用户ID不能为空'
                    }
                });
                return;
            }
            const providers = await user_provider_service_1.UserProviderService.getAvailableProviders(userId);
            res.json({
                success: true,
                data: providers
            });
        }
        catch (error) {
            console.error('获取用户可用接口失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: 'INTERNAL_ERROR',
                    message: '获取接口列表失败'
                }
            });
        }
    }
    static async getUserProviderStats(req, res) {
        try {
            const userId = parseInt(req.query.userId);
            const days = parseInt(req.query.days) || 30;
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_PARAMS',
                        message: '用户ID不能为空'
                    }
                });
                return;
            }
            const stats = await user_provider_service_1.UserProviderService.getUserProviderStats(userId, days);
            res.json({
                success: true,
                data: stats
            });
        }
        catch (error) {
            console.error('获取用户接口统计失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: 'INTERNAL_ERROR',
                    message: '获取统计数据失败'
                }
            });
        }
    }
    static async grantProviderPermission(req, res) {
        try {
            const { userId, providerId, grantedBy, expiresAt } = req.body;
            if (!userId || !providerId || !grantedBy) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_PARAMS',
                        message: '用户ID、接口ID和授权人ID不能为空'
                    }
                });
                return;
            }
            await user_provider_service_1.UserProviderService.grantProviderPermission(parseInt(userId), parseInt(providerId), parseInt(grantedBy), expiresAt ? new Date(expiresAt) : undefined);
            res.json({
                success: true,
                message: '授权成功'
            });
        }
        catch (error) {
            console.error('授权用户接口权限失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: 'INTERNAL_ERROR',
                    message: '授权失败'
                }
            });
        }
    }
    static async revokeProviderPermission(req, res) {
        try {
            const { userId, providerId } = req.body;
            if (!userId || !providerId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_PARAMS',
                        message: '用户ID和接口ID不能为空'
                    }
                });
                return;
            }
            await user_provider_service_1.UserProviderService.revokeProviderPermission(parseInt(userId), parseInt(providerId));
            res.json({
                success: true,
                message: '撤销权限成功'
            });
        }
        catch (error) {
            console.error('撤销用户接口权限失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: 'INTERNAL_ERROR',
                    message: '撤销权限失败'
                }
            });
        }
    }
    static async initializeLevelProviderVisibility(req, res) {
        try {
            await user_provider_service_1.UserProviderService.initializeLevelProviderVisibility();
            res.json({
                success: true,
                message: '初始化配置成功'
            });
        }
        catch (error) {
            console.error('初始化等级接口可见性配置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: 'INTERNAL_ERROR',
                    message: '初始化配置失败'
                }
            });
        }
    }
}
exports.UserProviderController = UserProviderController;
//# sourceMappingURL=user-provider.controller.js.map