"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const image_proxy_controller_1 = require("../controllers/image-proxy.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.requestId);
router.use(auth_middleware_1.recordIP);
router.get('/show/:imageId', image_proxy_controller_1.ImageProxyController.redirectToImage);
router.get('/info/:imageId', image_proxy_controller_1.ImageProxyController.getImageInfo);
exports.default = router;
//# sourceMappingURL=image-proxy.routes.js.map