"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QiniuProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const base_provider_1 = require("./base.provider");
class QiniuProvider extends base_provider_1.BaseProvider {
    constructor(config) {
        super(config);
    }
    async upload(fileBuffer, fileName, mimeType) {
        const startTime = Date.now();
        try {
            const validation = this.validateFile(fileBuffer, fileName, mimeType);
            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.error || '文件验证失败',
                    providerId: this.config.id,
                    providerName: this.config.name,
                };
            }
            const tokenEndpoint = this.config.config?.tokenEndpoint || 'https://zgtdxh.kejie.org.cn/ajax/get-qiniu-token.php?prefix=user';
            const tokenResponse = await axios_1.default.get(tokenEndpoint, {
                timeout: 10000,
            });
            if (!tokenResponse.data || !tokenResponse.data.data || !tokenResponse.data.data.token) {
                return {
                    success: false,
                    error: '获取七牛云token失败',
                    providerId: this.config.id,
                    providerName: this.config.name,
                };
            }
            const token = tokenResponse.data.data.token;
            const fileKey = this.generateQiniuFileKey(fileName);
            const formData = new form_data_1.default();
            formData.append('name', fileName);
            formData.append('chunk', '0');
            formData.append('chunks', '1');
            formData.append('key', fileKey);
            formData.append('token', token);
            formData.append('file', fileBuffer, {
                filename: fileName,
                contentType: mimeType
            });
            const uploadUrl = this.config.endpoint || 'https://upload.qbox.me/';
            const response = await axios_1.default.post(uploadUrl, formData, {
                headers: formData.getHeaders(),
                timeout: 30000,
            });
            const responseTime = Date.now() - startTime;
            if (response.data && response.data.key) {
                const baseUrl = this.config.config?.baseUrl || 'https://acad-upload.scimall.org.cn/';
                const finalUrl = baseUrl + response.data.key;
                return {
                    success: true,
                    url: finalUrl,
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                    metadata: {
                        key: response.data.key,
                        hash: response.data.hash,
                        bucket: response.data.bucket,
                    }
                };
            }
            else {
                return {
                    success: false,
                    error: '七牛云上传失败或响应格式错误',
                    providerId: this.config.id,
                    providerName: this.config.name,
                    responseTime,
                };
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                success: false,
                error: `七牛云上传失败: ${error instanceof Error ? error.message : '未知错误'}`,
                providerId: this.config.id,
                providerName: this.config.name,
                responseTime,
            };
        }
    }
    async healthCheck() {
        const startTime = Date.now();
        try {
            const tokenEndpoint = this.config.config?.tokenEndpoint || 'https://zgtdxh.kejie.org.cn/ajax/get-qiniu-token.php?prefix=user';
            const response = await axios_1.default.get(tokenEndpoint, {
                timeout: 10000,
            });
            const responseTime = Date.now() - startTime;
            if (response.data && response.data.data && response.data.data.token) {
                return {
                    isHealthy: true,
                    responseTime,
                    lastChecked: new Date(),
                };
            }
            else {
                return {
                    isHealthy: false,
                    error: 'Token接口响应格式错误',
                    responseTime,
                    lastChecked: new Date(),
                };
            }
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                isHealthy: false,
                error: error instanceof Error ? error.message : '健康检查失败',
                responseTime,
                lastChecked: new Date(),
            };
        }
    }
    generateQiniuFileKey(originalName) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        const uniqueId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
        const ext = originalName.split('.').pop() || 'png';
        return `${year}/${month}/${day}/${hour}/video/${uniqueId}.${ext}`;
    }
    async delete(url) {
        return {
            success: false,
            error: '七牛云删除功能需要管理凭证，暂未实现'
        };
    }
    async getFileInfo(url) {
        try {
            const response = await axios_1.default.head(url, {
                timeout: 10000,
            });
            return {
                success: true,
                data: {
                    contentType: response.headers['content-type'],
                    contentLength: response.headers['content-length'],
                    lastModified: response.headers['last-modified'],
                    etag: response.headers['etag'],
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取文件信息失败'
            };
        }
    }
}
exports.QiniuProvider = QiniuProvider;
//# sourceMappingURL=qiniu.provider.js.map