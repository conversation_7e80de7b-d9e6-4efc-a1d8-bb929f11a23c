-- 添加水滴云上传接口配置
-- 执行时间: 2024-07-06

-- 检查并恢复TCL接口（如果丢失）
DO $$
BEGIN
    -- 检查TCL接口是否存在
    IF NOT EXISTS (SELECT 1 FROM upload_providers WHERE name = 'TCL') THEN
        -- 恢复TCL接口
        INSERT INTO upload_providers (
            name,
            description,
            endpoint,
            api_key,
            config,
            status,
            priority,
            max_file_size,
            supported_formats,
            required_level,
            is_premium,
            cost_per_upload,
            created_at,
            updated_at
        ) VALUES (
            'TCL',
            'TCL第三方上传接口 - 非常规接口实现',
            'https://service2.tcl.com/api.php/Center/uploadQiniu',
            NULL,
            '{}',
            'active',
            10,
            10485760,
            ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'free',
            false,
            0.0000,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );

        -- 为TCL接口添加可见性配置
        INSERT INTO level_provider_visibility (
            level,
            provider_id,
            is_visible,
            display_order,
            created_at,
            updated_at
        )
        SELECT 
            level_name,
            (SELECT id FROM upload_providers WHERE name = 'TCL' LIMIT 1),
            true,
            10,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        FROM (
            VALUES 
            ('free'),
            ('vip1'),
            ('vip2'),
            ('vip3'),
            ('admin')
        ) AS levels(level_name);

        RAISE NOTICE 'TCL接口已恢复';
    ELSE
        RAISE NOTICE 'TCL接口已存在，无需恢复';
    END IF;
END $$;

-- 检查水滴云接口是否已存在
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM upload_providers 
        WHERE name = '水滴云' 
        OR endpoint = 'https://upload.shuidi.cn/uploadimage'
    ) THEN
        RAISE NOTICE '水滴云接口已存在，跳过创建';
    ELSE
        -- 插入水滴云接口配置
        INSERT INTO upload_providers (
            name,
            description,
            endpoint,
            api_key,
            config,
            status,
            priority,
            max_file_size,
            supported_formats,
            required_level,
            is_premium,
            cost_per_upload,
            created_at,
            updated_at
        ) VALUES (
            '水滴云',
            '水滴云图片上传服务 - 高可用CDN加速',
            'https://upload.shuidi.cn/uploadimage',
            NULL,
            '{"baseUrl": "https://filehuoshan.shuidi.cn/img/", "suffix": "/0x0.jpg"}',
            'active',
            11,
            20971520,
            ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'free',
            false,
            0.0000,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );

        -- 为所有用户等级添加水滴云接口可见性配置
        INSERT INTO level_provider_visibility (
            level,
            provider_id,
            is_visible,
            display_order,
            created_at,
            updated_at
        )
        SELECT 
            level_name,
            (SELECT id FROM upload_providers WHERE name = '水滴云' LIMIT 1),
            true,
            11,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        FROM (
            VALUES 
            ('free'),
            ('vip1'),
            ('vip2'),
            ('vip3'),
            ('admin')
        ) AS levels(level_name);

        RAISE NOTICE '水滴云接口配置完成';
    END IF;
END $$;

-- 验证配置结果
SELECT 
    '配置验证' as section,
    id,
    name,
    endpoint,
    status,
    priority,
    required_level,
    is_premium,
    max_file_size / 1024 / 1024 as max_size_mb
FROM upload_providers 
WHERE name IN ('TCL', '水滴云')
ORDER BY priority;

-- 验证可见性配置
SELECT 
    '可见性验证' as section,
    lpv.level,
    up.name as provider_name,
    lpv.is_visible,
    lpv.display_order
FROM level_provider_visibility lpv
JOIN upload_providers up ON lpv.provider_id = up.id
WHERE up.name IN ('TCL', '水滴云')
ORDER BY up.name, lpv.level;
