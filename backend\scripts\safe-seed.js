/**
 * 安全的数据库种子数据脚本
 * 处理所有可能的唯一性约束冲突
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function safeSeed() {
  console.log('🌱 开始安全的数据库种子数据初始化...\n');

  try {
    // 1. 创建用户等级配置
    console.log('1️⃣ 创建用户等级配置...');
    const levelConfigs = [
      {
        level: 'free',
        displayName: '免费用户',
        maxDailyUploads: 50,
        maxFileSize: BigInt(10 * 1024 * 1024), // 10MB
        maxStorageSpace: BigInt(1024 * 1024 * 1024), // 1GB
        canChooseProvider: false,
        prioritySupport: false,
        customDomain: false,
        visibleProviderCount: 3,
      },
      {
        level: 'vip1',
        displayName: 'VIP1用户',
        maxDailyUploads: 200,
        maxFileSize: BigInt(50 * 1024 * 1024), // 50MB
        maxStorageSpace: BigInt(10 * 1024 * 1024 * 1024), // 10GB
        canChooseProvider: true,
        prioritySupport: false,
        customDomain: false,
        visibleProviderCount: 5,
      },
      {
        level: 'vip2',
        displayName: 'VIP2用户',
        maxDailyUploads: 500,
        maxFileSize: BigInt(100 * 1024 * 1024), // 100MB
        maxStorageSpace: BigInt(50 * 1024 * 1024 * 1024), // 50GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 8,
      },
      {
        level: 'vip3',
        displayName: 'VIP3用户',
        maxDailyUploads: 2000,
        maxFileSize: BigInt(500 * 1024 * 1024), // 500MB
        maxStorageSpace: BigInt(200 * 1024 * 1024 * 1024), // 200GB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 10,
      },
      {
        level: 'admin',
        displayName: '管理员',
        maxDailyUploads: 10000,
        maxFileSize: BigInt(1024 * 1024 * 1024), // 1GB
        maxStorageSpace: BigInt(1024 * 1024 * 1024 * 1024), // 1TB
        canChooseProvider: true,
        prioritySupport: true,
        customDomain: true,
        visibleProviderCount: 20,
      },
    ];

    for (const config of levelConfigs) {
      try {
        await prisma.userLevelConfig.upsert({
          where: { level: config.level },
          update: config,
          create: config,
        });
        console.log(`✅ 用户等级配置: ${config.displayName}`);
      } catch (error) {
        console.log(`⚠️ 用户等级配置 ${config.level} 已存在或创建失败: ${error.message}`);
      }
    }

    // 2. 创建上传提供商
    console.log('\n2️⃣ 创建上传提供商...');
    const providers = [
      {
        id: 1,
        name: 'TCL',
        displayName: 'TCL云存储',
        endpoint: 'https://service2.tcl.com/api.php/Center/uploadQiniu',
        status: 'active',
        priority: 10,
        requiredLevel: 'free',
        isPremium: false,
        config: {
          method: 'POST',
          fileField: 'file',
          responseFormat: 'json',
          dataPath: 'data'
        }
      }
    ];

    for (const provider of providers) {
      try {
        await prisma.uploadProvider.upsert({
          where: { id: provider.id },
          update: provider,
          create: provider,
        });
        console.log(`✅ 上传提供商: ${provider.displayName}`);
      } catch (error) {
        console.log(`⚠️ 上传提供商 ${provider.name} 已存在或创建失败: ${error.message}`);
      }
    }

    // 3. 创建等级可见性配置
    console.log('\n3️⃣ 创建等级可见性配置...');
    const visibilityConfigs = [
      // TCL对所有等级可见
      { level: 'free', providerId: 1, isVisible: true, displayOrder: 1 },
      { level: 'vip1', providerId: 1, isVisible: true, displayOrder: 1 },
      { level: 'vip2', providerId: 1, isVisible: true, displayOrder: 1 },
      { level: 'vip3', providerId: 1, isVisible: true, displayOrder: 1 },
      { level: 'admin', providerId: 1, isVisible: true, displayOrder: 1 },
    ];

    for (const config of visibilityConfigs) {
      try {
        await prisma.levelProviderVisibility.upsert({
          where: {
            level_providerId: {
              level: config.level,
              providerId: config.providerId
            }
          },
          update: config,
          create: config,
        });
        console.log(`✅ 可见性配置: ${config.level} -> 提供商${config.providerId}`);
      } catch (error) {
        console.log(`⚠️ 可见性配置失败: ${error.message}`);
      }
    }

    // 4. 创建系统配置
    console.log('\n4️⃣ 创建系统配置...');
    const systemConfigs = [
      {
        key: 'site_name',
        value: 'LoftChat',
        description: '网站名称',
        category: 'general'
      },
      {
        key: 'max_file_size',
        value: '100',
        description: '最大文件大小(MB)',
        category: 'upload'
      },
      {
        key: 'allowed_file_types',
        value: 'jpg,jpeg,png,gif,webp',
        description: '允许的文件类型',
        category: 'upload'
      }
    ];

    for (const config of systemConfigs) {
      try {
        await prisma.systemConfig.upsert({
          where: { key: config.key },
          update: { value: config.value, description: config.description },
          create: config,
        });
        console.log(`✅ 系统配置: ${config.key}`);
      } catch (error) {
        console.log(`⚠️ 系统配置 ${config.key} 已存在或创建失败: ${error.message}`);
      }
    }

    // 5. 创建默认管理员用户（安全方式）
    console.log('\n5️⃣ 创建默认管理员用户...');
    
    // 检查是否已存在admin用户
    const existingAdmin = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { email: '<EMAIL>' }
        ]
      }
    });

    if (!existingAdmin) {
      try {
        const adminPassword = await bcrypt.hash('Admin123', 12);
        await prisma.user.create({
          data: {
            username: 'admin',
            email: '<EMAIL>',
            passwordHash: adminPassword,
            role: 'admin',
            userLevel: 'admin',
            status: 'active',
          },
        });
        console.log('✅ 创建默认管理员用户: admin');
        console.log('📧 邮箱: <EMAIL>');
        console.log('🔑 密码: Admin123');
      } catch (error) {
        console.log(`❌ 创建管理员用户失败: ${error.message}`);
      }
    } else {
      console.log('ℹ️ 管理员用户已存在，跳过创建');
      console.log(`📋 现有管理员: ${existingAdmin.username} (${existingAdmin.email})`);
    }

    // 6. 验证种子数据
    console.log('\n6️⃣ 验证种子数据...');
    
    const levelCount = await prisma.userLevelConfig.count();
    const providerCount = await prisma.uploadProvider.count();
    const visibilityCount = await prisma.levelProviderVisibility.count();
    const configCount = await prisma.systemConfig.count();
    const userCount = await prisma.user.count();

    console.log('📊 数据统计:');
    console.log(`   用户等级配置: ${levelCount} 个`);
    console.log(`   上传提供商: ${providerCount} 个`);
    console.log(`   可见性配置: ${visibilityCount} 个`);
    console.log(`   系统配置: ${configCount} 个`);
    console.log(`   用户数量: ${userCount} 个`);

    console.log('\n🎉 数据库种子数据初始化完成！');

  } catch (error) {
    console.error('❌ 种子数据初始化失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 执行脚本
if (require.main === module) {
  safeSeed()
    .then(() => {
      console.log('\n✨ 安全种子数据脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 安全种子数据脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { safeSeed };
