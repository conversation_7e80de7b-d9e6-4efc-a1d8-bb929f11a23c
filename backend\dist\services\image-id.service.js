"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageIdService = void 0;
const database_1 = require("../config/database");
const id_generator_1 = require("../utils/id-generator");
class ImageIdService {
    static async generatePublicId(imageId) {
        if (this.idMap.has(imageId)) {
            return this.idMap.get(imageId);
        }
        const publicId = await this.generateConsistentId(imageId);
        this.idMap.set(imageId, publicId);
        this.reverseMap.set(publicId, imageId);
        return publicId;
    }
    static async getInternalId(publicId) {
        if (!id_generator_1.IdGenerator.validateImageId(publicId)) {
            return null;
        }
        if (this.reverseMap.has(publicId)) {
            return this.reverseMap.get(publicId);
        }
        const images = await database_1.prisma.image.findMany({
            select: { id: true }
        });
        for (const image of images) {
            const generatedId = await this.generateConsistentId(image.id);
            if (generatedId === publicId) {
                this.idMap.set(image.id, publicId);
                this.reverseMap.set(publicId, image.id);
                return image.id;
            }
        }
        return null;
    }
    static async generateConsistentId(imageId) {
        const seed = imageId.toString().padStart(8, '0');
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
        let result = '';
        for (let i = 0; i < 16; i++) {
            const hash = this.simpleHash(seed + i.toString());
            const index = hash % chars.length;
            result += chars[index];
        }
        return result;
    }
    static simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash);
    }
    static async generatePublicIdsForExistingImages() {
        const images = await database_1.prisma.image.findMany({
            select: { id: true }
        });
        console.log(`开始为 ${images.length} 张图片生成公开ID...`);
        for (const image of images) {
            const publicId = await this.generatePublicId(image.id);
            console.log(`图片 ${image.id} -> ${publicId}`);
        }
        console.log('公开ID生成完成！');
    }
    static clearCache() {
        this.idMap.clear();
        this.reverseMap.clear();
    }
    static getCacheStats() {
        return {
            mappings: this.idMap.size,
            reverseMappings: this.reverseMap.size
        };
    }
}
exports.ImageIdService = ImageIdService;
ImageIdService.idMap = new Map();
ImageIdService.reverseMap = new Map();
//# sourceMappingURL=image-id.service.js.map