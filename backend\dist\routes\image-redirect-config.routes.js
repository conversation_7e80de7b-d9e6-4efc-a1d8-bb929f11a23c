"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const image_redirect_config_controller_1 = require("../controllers/image-redirect-config.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const validation_middleware_1 = require("../middleware/validation.middleware");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.requestId);
router.use(auth_middleware_1.recordIP);
router.use(auth_middleware_1.authenticateToken);
router.get('/providers', image_redirect_config_controller_1.ImageRedirectConfigController.getUserAvailableProviders);
router.get('/global', image_redirect_config_controller_1.ImageRedirectConfigController.getUserGlobalConfig);
router.post('/global', (0, express_validator_1.body)('redirectMode')
    .isIn(['auto', 'manual'])
    .withMessage('跳转模式必须是 auto 或 manual'), (0, express_validator_1.body)('preferredProviderId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('提供商ID必须是正整数'), (0, express_validator_1.body)('isActive')
    .optional()
    .isBoolean()
    .withMessage('启用状态必须是布尔值'), validation_middleware_1.validateRequest, image_redirect_config_controller_1.ImageRedirectConfigController.saveUserGlobalConfig);
router.get('/:imageId', (0, express_validator_1.param)('imageId').isInt({ min: 1 }).withMessage('图片ID必须是正整数'), validation_middleware_1.validateRequest, image_redirect_config_controller_1.ImageRedirectConfigController.getUserImageConfig);
router.put('/:imageId', (0, express_validator_1.param)('imageId').isInt({ min: 1 }).withMessage('图片ID必须是正整数'), (0, express_validator_1.body)('redirectStrategy')
    .optional()
    .isIn(['auto', 'provider', 'priority'])
    .withMessage('跳转策略必须是 auto、provider 或 priority'), (0, express_validator_1.body)('preferredProviderId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('提供商ID必须是正整数'), (0, express_validator_1.body)('fallbackStrategy')
    .optional()
    .isIn(['auto', 'priority'])
    .withMessage('备用策略必须是 auto 或 priority'), (0, express_validator_1.body)('isEnabled')
    .optional()
    .isBoolean()
    .withMessage('启用状态必须是布尔值'), validation_middleware_1.validateRequest, image_redirect_config_controller_1.ImageRedirectConfigController.setUserImageConfig);
router.post('/batch', (0, express_validator_1.body)('imageIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('图片ID数组长度必须在1-100之间'), (0, express_validator_1.body)('imageIds.*')
    .isInt({ min: 1 })
    .withMessage('每个图片ID必须是正整数'), validation_middleware_1.validateRequest, image_redirect_config_controller_1.ImageRedirectConfigController.getBatchUserImageConfigs);
exports.default = router;
//# sourceMappingURL=image-redirect-config.routes.js.map