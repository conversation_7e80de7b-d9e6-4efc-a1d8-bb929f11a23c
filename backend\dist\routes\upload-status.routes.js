"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const upload_status_controller_1 = require("../controllers/upload-status.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const express_validator_1 = require("express-validator");
const validation_middleware_1 = require("../middleware/validation.middleware");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.requestId);
router.use(auth_middleware_1.recordIP);
router.use(auth_middleware_1.authenticateToken);
router.get('/image/:imageId', (0, express_validator_1.param)('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'), validation_middleware_1.validateRequest, upload_status_controller_1.UploadStatusController.getImageStatus);
router.get('/history', (0, express_validator_1.query)('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'), (0, express_validator_1.query)('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'), validation_middleware_1.validateRequest, upload_status_controller_1.UploadStatusController.getUploadHistory);
router.get('/image/:imageId/backup-stats', (0, express_validator_1.param)('imageId')
    .isInt({ min: 1 })
    .withMessage('图片ID必须是正整数'), validation_middleware_1.validateRequest, upload_status_controller_1.UploadStatusController.getBackupStats);
router.get('/providers-overview', upload_status_controller_1.UploadStatusController.getProvidersOverview);
exports.default = router;
//# sourceMappingURL=upload-status.routes.js.map