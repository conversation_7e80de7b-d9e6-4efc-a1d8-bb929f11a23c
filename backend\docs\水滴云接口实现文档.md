# 水滴云第三方上传接口实现文档

## 概述

本文档记录了水滴云第三方上传接口的完整实现过程，该接口基于提供的PHP代码进行Node.js适配。

## 接口信息

- **接口名称**: 水滴云
- **接口地址**: `https://upload.shuidi.cn/uploadimage`
- **请求方式**: POST
- **请求格式**: multipart/form-data
- **文件字段**: `file`
- **响应格式**: JSON
- **数据提取**: 从响应的 `path` 字段提取路径，然后拼接为完整URL

## PHP原始代码参考

```php
<?php
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_FILES['file'])) {
    $file = $_FILES['file']['tmp_name'];
    $file_name = $_FILES['file']['name'];

    // 初始化 cURL
    $ch = curl_init();

    // 设置 cURL 选项
    curl_setopt($ch, CURLOPT_URL, 'https://upload.shuidi.cn/uploadimage');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    // 构建请求体数据
    $post_data = array(
        'file' => curl_file_create($file, 'image/jpeg', $file_name)
    );

    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);

    // 执行 cURL 请求并获取响应
    $response = curl_exec($ch);
    curl_close($ch);

    // 解析响应并提取路径
    $response_data = json_decode($response, true);
    $path = $response_data['path'];

    // 补全路径为指定格式
    $full_path = 'https://filehuoshan.shuidi.cn/img/' . $path . '/0x0.jpg';

    // 直接输出完整路径
    echo $full_path;
}
?>
```

## 实现详情

### 1. 独立接口适配器

创建了 `backend/src/providers/shuidi.provider.ts` 文件，实现了完整的水滴云接口适配器。

### 2. 上传服务集成

在 `backend/src/services/upload-provider.service.ts` 中添加了：

1. **Switch 分支扩展**：
   ```typescript
   case '水滴云':
   case 'shuidi':
   case 'shuidi云':
     result = await UploadProviderService.uploadToShuidi(provider, fileBuffer, fileName, mimeType);
     break;
   ```

2. **专用上传方法**：
   ```typescript
   private static async uploadToShuidi(
     provider: ProviderConfig,
     fileBuffer: Buffer,
     fileName: string,
     mimeType: string
   ): Promise<UploadResult>
   ```

### 3. 数据库配置

#### 接口配置
```sql
INSERT INTO upload_providers (
    name,
    description,
    endpoint,
    api_key,
    config,
    status,
    priority,
    max_file_size,
    supported_formats,
    required_level,
    is_premium,
    cost_per_upload
) VALUES (
    '水滴云',
    '水滴云图片上传服务 - 高可用CDN加速',
    'https://upload.shuidi.cn/uploadimage',
    NULL,
    '{"baseUrl": "https://filehuoshan.shuidi.cn/img/", "suffix": "/0x0.jpg"}',
    'active',
    11,
    20971520, -- 20MB
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    'free',
    false,
    0.0000
);
```

#### 用户等级可见性配置
为所有用户等级（free, vip1, vip2, vip3, admin）添加可见性配置。

## 核心特性

### 1. URL拼接逻辑
- 基础URL: `https://filehuoshan.shuidi.cn/img/`
- 路径: 从响应的 `path` 字段获取
- 后缀: `/0x0.jpg`
- 最终URL: `baseUrl + path + suffix`

### 2. 错误处理
- 网络超时处理（30秒）
- 响应数据验证
- 详细的错误信息返回

### 3. 文件验证
- 支持的格式：JPEG, PNG, GIF, WebP
- 最大文件大小：20MB
- MIME类型验证

## 手动执行步骤

如果自动脚本执行失败，可以手动执行以下步骤：

### 1. 数据库操作

```sql
-- 添加水滴云接口
INSERT INTO upload_providers (
    name, description, endpoint, api_key, config, status, priority,
    max_file_size, supported_formats, required_level, is_premium, cost_per_upload,
    created_at, updated_at
) VALUES (
    '水滴云',
    '水滴云图片上传服务 - 高可用CDN加速',
    'https://upload.shuidi.cn/uploadimage',
    NULL,
    '{"baseUrl": "https://filehuoshan.shuidi.cn/img/", "suffix": "/0x0.jpg"}',
    'active',
    11,
    20971520,
    ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    'free',
    false,
    0.0000,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 添加可见性配置
INSERT INTO level_provider_visibility (level, provider_id, is_visible, display_order, created_at, updated_at)
SELECT level_name, (SELECT id FROM upload_providers WHERE name = '水滴云' LIMIT 1), true, 11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM (VALUES ('free'), ('vip1'), ('vip2'), ('vip3'), ('admin')) AS levels(level_name);
```

### 2. 重启服务

代码修改完成后，重启后端服务以加载新的上传方法。

## 测试验证

### 1. 接口可用性测试
- 检查接口是否在用户可选列表中显示
- 测试文件上传功能
- 验证返回的URL是否可访问

### 2. 错误处理测试
- 测试网络超时情况
- 测试无效文件格式
- 测试超大文件上传

## 注意事项

1. **网络依赖**: 该接口依赖外部服务，需要稳定的网络连接
2. **响应格式**: 严格依赖响应中的 `path` 字段
3. **URL格式**: 最终URL格式固定，不支持自定义
4. **文件限制**: 仅支持图片文件，其他格式会被拒绝

## 故障排除

### 常见问题
1. **上传失败**: 检查网络连接和接口可用性
2. **URL无法访问**: 验证path字段和URL拼接逻辑
3. **文件格式错误**: 确认文件类型在支持列表中

### 日志监控
建议监控以下日志：
- 上传请求日志
- 响应时间统计
- 错误率统计
