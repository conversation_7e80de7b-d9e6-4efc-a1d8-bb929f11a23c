"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageRedirectConfigController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
class ImageRedirectConfigController {
    static async getUserImageConfig(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            const { imageId } = req.params;
            if (!imageId || isNaN(parseInt(imageId))) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的图片ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const imageIdInt = parseInt(imageId);
            const userImage = await database_1.prisma.userImage.findUnique({
                where: {
                    userId_imageId: {
                        userId,
                        imageId: imageIdInt
                    }
                }
            });
            if (!userImage) {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.FORBIDDEN,
                        message: '无权限访问该图片',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const config = await database_1.prisma.userImageRedirectConfig.findUnique({
                where: {
                    userId_imageId: {
                        userId,
                        imageId: imageIdInt
                    }
                },
                include: {
                    preferredProvider: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            status: true
                        }
                    }
                }
            });
            const availableProviders = await ImageRedirectConfigController.getAvailableProviders(userId, imageIdInt);
            res.json({
                success: true,
                data: {
                    imageId: imageIdInt,
                    config: config ? {
                        redirectStrategy: config.redirectStrategy,
                        preferredProviderId: config.preferredProviderId,
                        preferredProvider: config.preferredProvider,
                        fallbackStrategy: config.fallbackStrategy,
                        isEnabled: config.isEnabled,
                        updatedAt: config.updatedAt
                    } : {
                        redirectStrategy: 'auto',
                        preferredProviderId: null,
                        preferredProvider: null,
                        fallbackStrategy: 'auto',
                        isEnabled: true,
                        updatedAt: null
                    },
                    availableProviders
                }
            });
        }
        catch (error) {
            console.error('获取用户图片配置错误:', error);
            next(error);
        }
    }
    static async setUserImageConfig(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            const { imageId } = req.params;
            const { redirectStrategy, preferredProviderId, fallbackStrategy, isEnabled } = req.body;
            if (!imageId || isNaN(parseInt(imageId))) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的图片ID',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const imageIdInt = parseInt(imageId);
            const userImage = await database_1.prisma.userImage.findUnique({
                where: {
                    userId_imageId: {
                        userId,
                        imageId: imageIdInt
                    }
                }
            });
            if (!userImage) {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.FORBIDDEN,
                        message: '无权限访问该图片',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const validStrategies = ['auto', 'provider', 'priority'];
            if (redirectStrategy && !validStrategies.includes(redirectStrategy)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的跳转策略',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (preferredProviderId) {
                const availableProviders = await ImageRedirectConfigController.getAvailableProviders(userId, imageIdInt);
                const isProviderAvailable = availableProviders.some(p => p.id === preferredProviderId);
                if (!isProviderAvailable) {
                    res.status(400).json({
                        success: false,
                        error: {
                            code: types_1.ErrorCodes.INVALID_INPUT,
                            message: '指定的提供商不可用',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
            }
            const config = await database_1.prisma.userImageRedirectConfig.upsert({
                where: {
                    userId_imageId: {
                        userId,
                        imageId: imageIdInt
                    }
                },
                update: {
                    redirectStrategy: redirectStrategy || 'auto',
                    preferredProviderId: preferredProviderId || null,
                    fallbackStrategy: fallbackStrategy || 'auto',
                    isEnabled: isEnabled !== undefined ? isEnabled : true,
                },
                create: {
                    userId,
                    imageId: imageIdInt,
                    redirectStrategy: redirectStrategy || 'auto',
                    preferredProviderId: preferredProviderId || null,
                    fallbackStrategy: fallbackStrategy || 'auto',
                    isEnabled: isEnabled !== undefined ? isEnabled : true,
                },
                include: {
                    preferredProvider: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            status: true
                        }
                    }
                }
            });
            res.json({
                success: true,
                message: '跳转配置已更新',
                data: {
                    imageId: imageIdInt,
                    config: {
                        redirectStrategy: config.redirectStrategy,
                        preferredProviderId: config.preferredProviderId,
                        preferredProvider: config.preferredProvider,
                        fallbackStrategy: config.fallbackStrategy,
                        isEnabled: config.isEnabled,
                        updatedAt: config.updatedAt
                    }
                }
            });
        }
        catch (error) {
            console.error('设置用户图片配置错误:', error);
            next(error);
        }
    }
    static async getUserAvailableProviders(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { userLevel: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const providers = await ImageRedirectConfigController.getAvailableProviders(userId);
            res.json({
                success: true,
                data: {
                    userLevel: user.userLevel,
                    providers
                }
            });
        }
        catch (error) {
            console.error('获取可用提供商错误:', error);
            next(error);
        }
    }
    static async getUserGlobalConfig(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '用户未登录',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const userConfig = await database_1.prisma.userRedirectConfig.findUnique({
                where: { userId }
            });
            const config = userConfig || {
                redirectMode: 'auto',
                preferredProviderId: null,
                isActive: true
            };
            res.json({
                success: true,
                data: {
                    redirectMode: config.redirectMode,
                    preferredProviderId: config.preferredProviderId,
                    isActive: config.isActive,
                    hasCustomConfig: !!userConfig
                }
            });
        }
        catch (error) {
            console.error('获取用户全局配置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户全局配置失败',
                    timestamp: new Date().toISOString(),
                }
            });
        }
    }
    static async saveUserGlobalConfig(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '用户未登录',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const { redirectMode, preferredProviderId, isActive } = req.body;
            if (!redirectMode || !['auto', 'manual'].includes(redirectMode)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '跳转模式必须是 auto 或 manual',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (redirectMode === 'manual' && preferredProviderId) {
                const provider = await database_1.prisma.uploadProvider.findUnique({
                    where: { id: preferredProviderId }
                });
                if (!provider || provider.status !== 'active') {
                    res.status(400).json({
                        success: false,
                        error: {
                            code: types_1.ErrorCodes.INVALID_INPUT,
                            message: '指定的提供商不存在或不可用',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
            }
            const savedConfig = await database_1.prisma.userRedirectConfig.upsert({
                where: { userId },
                update: {
                    redirectMode,
                    preferredProviderId: redirectMode === 'manual' ? preferredProviderId : null,
                    isActive: isActive !== undefined ? isActive : true,
                    updatedAt: new Date()
                },
                create: {
                    userId,
                    redirectMode,
                    preferredProviderId: redirectMode === 'manual' ? preferredProviderId : null,
                    isActive: isActive !== undefined ? isActive : true
                }
            });
            res.json({
                success: true,
                message: '跳转配置保存成功',
                data: {
                    redirectMode: savedConfig.redirectMode,
                    preferredProviderId: savedConfig.preferredProviderId,
                    isActive: savedConfig.isActive,
                    updatedAt: savedConfig.updatedAt.toISOString()
                }
            });
        }
        catch (error) {
            console.error('保存用户全局配置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '保存用户全局配置失败',
                    timestamp: new Date().toISOString(),
                }
            });
        }
    }
    static async getAvailableProviders(userId, imageId) {
        const user = await database_1.prisma.user.findUnique({
            where: { id: userId },
            select: { userLevel: true }
        });
        if (!user)
            return [];
        const levelVisibility = await database_1.prisma.levelProviderVisibility.findMany({
            where: {
                level: user.userLevel,
                isVisible: true
            },
            include: {
                provider: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                        status: true,
                        isPremium: true,
                        priority: true
                    }
                }
            }
        });
        let availableProviders = levelVisibility.map(lv => lv.provider);
        if (imageId) {
            const imageLinks = await database_1.prisma.imageLink.findMany({
                where: { imageId },
                select: { providerId: true }
            });
            const linkedProviderIds = imageLinks.map(link => link.providerId);
            availableProviders = availableProviders.filter(provider => linkedProviderIds.includes(provider.id));
        }
        return availableProviders.filter(provider => provider.status === 'active');
    }
    static async getBatchUserImageConfigs(req, res, next) {
        try {
            const userId = parseInt(req.user.id);
            const { imageIds } = req.body;
            if (!Array.isArray(imageIds) || imageIds.length === 0) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '请提供有效的图片ID数组',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const imageIdInts = imageIds.map(id => parseInt(id)).filter(id => !isNaN(id));
            const userImages = await database_1.prisma.userImage.findMany({
                where: {
                    userId,
                    imageId: { in: imageIdInts }
                },
                select: { imageId: true }
            });
            const accessibleImageIds = userImages.map(ui => ui.imageId);
            const configs = await database_1.prisma.userImageRedirectConfig.findMany({
                where: {
                    userId,
                    imageId: { in: accessibleImageIds }
                },
                include: {
                    preferredProvider: {
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            status: true
                        }
                    }
                }
            });
            const configMap = new Map(configs.map(config => [config.imageId, config]));
            const results = accessibleImageIds.map(imageId => ({
                imageId,
                config: configMap.get(imageId) || {
                    redirectStrategy: 'auto',
                    preferredProviderId: null,
                    preferredProvider: null,
                    fallbackStrategy: 'auto',
                    isEnabled: true,
                    updatedAt: null
                }
            }));
            res.json({
                success: true,
                data: results
            });
        }
        catch (error) {
            console.error('批量获取用户图片配置错误:', error);
            next(error);
        }
    }
}
exports.ImageRedirectConfigController = ImageRedirectConfigController;
//# sourceMappingURL=image-redirect-config.controller.js.map