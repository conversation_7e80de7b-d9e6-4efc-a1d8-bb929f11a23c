const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('🔍 检查上传接口优先级:');
    const providers = await prisma.uploadProvider.findMany({
      where: { name: { in: ['TCL', '水滴云'] } },
      select: { id: true, name: true, priority: true, status: true },
      orderBy: { priority: 'asc' }
    });
    
    providers.forEach(p => {
      console.log(`  ${p.name}: ID=${p.id}, 优先级=${p.priority}, 状态=${p.status}`);
    });
    
    console.log('\n🔍 检查最近的图片链接:');
    const recentLinks = await prisma.imageLink.findMany({
      include: {
        provider: { select: { id: true, name: true, priority: true } },
        image: { select: { id: true, originalName: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });
    
    recentLinks.forEach(link => {
      console.log(`  图片${link.imageId} (${link.image.originalName}): ${link.provider.name} (ID=${link.providerId}, 优先级=${link.provider.priority})`);
    });
    
    console.log('\n🔍 检查最近上传的图片详情:');
    const recentImage = await prisma.image.findFirst({
      include: {
        imageLinks: {
          include: {
            provider: { select: { id: true, name: true, priority: true } }
          },
          orderBy: { createdAt: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    
    if (recentImage) {
      console.log(`\n最新图片 ${recentImage.id} (${recentImage.originalName}):`);
      console.log(`  系统链接: ${recentImage.systemUrl}`);
      console.log(`  上传状态: ${recentImage.uploadStatus}`);
      console.log(`  链接数量: ${recentImage.imageLinks.length}`);
      
      recentImage.imageLinks.forEach((link, index) => {
        console.log(`  链接${index + 1}: ${link.provider.name} (ID=${link.providerId}, 优先级=${link.provider.priority})`);
        console.log(`    状态: ${link.status}`);
        console.log(`    URL: ${link.externalUrl}`);
      });
    }
    
  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();
