"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
const file_service_1 = require("../services/file.service");
const image_processing_service_1 = require("../services/image-processing.service");
const upload_provider_service_1 = require("../services/upload-provider.service");
const user_provider_service_1 = require("../services/user-provider.service");
const queue_service_1 = require("../services/queue.service");
const image_id_service_1 = require("../services/image-id.service");
const crypto_1 = __importDefault(require("crypto"));
const path_1 = __importDefault(require("path"));
class UploadController {
    static async uploadSingle(req, res, next) {
        try {
            const userId = req.user?.id;
            const file = req.file;
            const { providerId } = req.body;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (!file) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '文件不能为空',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const canUpload = await UploadController.checkUploadLimits(parseInt(userId), file.size);
            if (!canUpload.allowed) {
                res.status(403).json({
                    success: false,
                    error: {
                        code: canUpload.errorCode,
                        message: canUpload.message,
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (providerId) {
                const canUseProvider = await user_provider_service_1.UserProviderService.canUseProvider(parseInt(userId), parseInt(providerId));
                if (!canUseProvider) {
                    res.status(403).json({
                        success: false,
                        error: {
                            code: types_1.ErrorCodes.FORBIDDEN,
                            message: '您没有权限使用指定的接口',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
            }
            const fileBuffer = file.buffer;
            const fileHash = crypto_1.default.createHash('sha256').update(fileBuffer).digest('hex');
            const fileMd5 = crypto_1.default.createHash('md5').update(fileBuffer).digest('hex');
            const existingImage = await database_1.prisma.image.findUnique({
                where: { fileHash },
                include: {
                    userImages: {
                        where: { userId: parseInt(userId) }
                    },
                    imageLinks: {
                        include: {
                            provider: true
                        }
                    }
                }
            });
            if (existingImage) {
                if (existingImage.userImages.length === 0) {
                    await database_1.prisma.userImage.create({
                        data: {
                            userId: parseInt(userId),
                            imageId: existingImage.id,
                            isOriginalUploader: false,
                        }
                    });
                }
                await UploadController.logUpload(parseInt(userId), existingImage.id, 'reuse', req);
                const publicId = await image_id_service_1.ImageIdService.generatePublicId(existingImage.id);
                let systemUrl = existingImage.systemUrl;
                if (!systemUrl.startsWith('/image/show/')) {
                    systemUrl = `/image/show/${publicId}`;
                    await database_1.prisma.image.update({
                        where: { id: existingImage.id },
                        data: { systemUrl }
                    });
                }
                const baseUrl = `${req.protocol}://${req.get('host')}`;
                const fullSystemUrl = systemUrl.startsWith('http') ? systemUrl : `${baseUrl}${systemUrl}`;
                const response = {
                    success: true,
                    message: '文件已存在，直接返回链接',
                    data: {
                        imageId: publicId,
                        systemUrl: fullSystemUrl,
                        isReused: true,
                        uploadTime: new Date().toISOString(),
                        fileSize: Number(existingImage.fileSize),
                        mimeType: existingImage.mimeType,
                    }
                };
                res.json(response);
                return;
            }
            const imageInfo = await image_processing_service_1.ImageProcessingService.getImageInfo(fileBuffer, file.mimetype);
            const fileName = `${fileHash}${path_1.default.extname(file.originalname)}`;
            const filePath = await file_service_1.FileService.saveFile(fileBuffer, fileName);
            const image = await database_1.prisma.image.create({
                data: {
                    originalName: file.originalname,
                    fileHash,
                    fileMd5,
                    fileSize: BigInt(file.size),
                    mimeType: file.mimetype,
                    width: imageInfo.width ?? null,
                    height: imageInfo.height ?? null,
                    systemUrl: `/api/images/${fileHash}`,
                    uploadStatus: 'processing',
                }
            });
            const publicId = await image_id_service_1.ImageIdService.generatePublicId(image.id);
            await database_1.prisma.image.update({
                where: { id: image.id },
                data: {
                    systemUrl: `/image/show/${publicId}`
                }
            });
            await database_1.prisma.userImage.create({
                data: {
                    userId: parseInt(userId),
                    imageId: image.id,
                    isOriginalUploader: true,
                }
            });
            await UploadController.logUpload(parseInt(userId), image.id, 'upload', req);
            const userInfo = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { userLevel: true }
            });
            let primaryUploadResult = null;
            const availableProviders = await upload_provider_service_1.UploadProviderService.getAvailableProviders(parseInt(userId), userInfo?.userLevel || 'free');
            if (availableProviders.length > 0) {
                const primaryProvider = providerId
                    ? availableProviders.find(p => p.id === parseInt(providerId))
                    : availableProviders[0];
                if (primaryProvider) {
                    try {
                        console.log(`🚀 开始上传到主接口: ${primaryProvider.name}`);
                        const fileBuffer = await file_service_1.FileService.readFile(filePath);
                        primaryUploadResult = await upload_provider_service_1.UploadProviderService.uploadToProvider(primaryProvider, fileBuffer, file.originalname, file.mimetype);
                        if (primaryUploadResult.success) {
                            await database_1.prisma.imageLink.create({
                                data: {
                                    imageId: image.id,
                                    providerId: primaryProvider.id,
                                    externalUrl: primaryUploadResult.url,
                                    status: 'active',
                                    responseTime: primaryUploadResult.responseTime ?? null,
                                    lastChecked: new Date(),
                                }
                            });
                            await database_1.prisma.image.update({
                                where: { id: image.id },
                                data: { uploadStatus: 'completed' }
                            });
                            if (providerId) {
                                await database_1.prisma.userImageRedirectConfig.create({
                                    data: {
                                        userId: parseInt(userId),
                                        imageId: image.id,
                                        redirectStrategy: 'provider',
                                        preferredProviderId: primaryProvider.id,
                                        fallbackStrategy: 'auto',
                                        isEnabled: true,
                                    }
                                }).catch(error => {
                                    console.log('跳转配置已存在或创建失败:', error.message);
                                });
                            }
                            console.log(`✅ 主接口 ${primaryProvider.name} 上传成功`);
                        }
                    }
                    catch (error) {
                        console.error(`❌ 主接口 ${primaryProvider.name} 上传失败:`, error);
                    }
                }
            }
            const jobData = {
                imageId: image.id,
                userId: parseInt(userId),
                filePath,
                primaryProviderId: primaryUploadResult?.providerId,
                fileInfo: {
                    originalName: file.originalname,
                    mimeType: file.mimetype,
                    size: file.size,
                    hash: fileHash,
                }
            };
            await queue_service_1.QueueService.addUploadJob(jobData);
            const baseUrl = `${req.protocol}://${req.get('host')}`;
            const fullSystemUrl = `${baseUrl}/image/show/${publicId}`;
            const response = {
                success: true,
                message: '文件上传成功',
                data: {
                    imageId: publicId,
                    systemUrl: fullSystemUrl,
                    isReused: false,
                    uploadTime: new Date().toISOString(),
                    fileSize: file.size,
                    mimeType: file.mimetype,
                }
            };
            res.status(201).json(response);
        }
        catch (error) {
            console.error('上传文件错误:', error);
            next(error);
        }
    }
    static async uploadBatch(req, res, next) {
        try {
            const userId = req.user?.id;
            let files = [];
            console.log('📋 批量上传调试信息:');
            console.log('req.files 类型:', typeof req.files);
            console.log('req.files 内容:', req.files);
            console.log('req.file 内容:', req.file);
            if (req.files) {
                if (Array.isArray(req.files)) {
                    files = req.files;
                    console.log('✅ 使用数组格式，文件数量:', files.length);
                }
                else {
                    const fileFields = req.files;
                    files = [
                        ...(fileFields.files || []),
                        ...(fileFields.file || [])
                    ];
                    console.log('✅ 使用对象格式，文件数量:', files.length);
                    console.log('fileFields.files:', fileFields.files?.length || 0);
                    console.log('fileFields.file:', fileFields.file?.length || 0);
                }
            }
            else if (req.file) {
                files = [req.file];
                console.log('✅ 使用单文件格式，文件数量:', files.length);
            }
            console.log('📊 最终文件数量:', files.length);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (!files || files.length === 0) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '文件不能为空',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const results = [];
            const errors = [];
            for (const file of files) {
                try {
                    const canUpload = await UploadController.checkUploadLimits(parseInt(userId), file.size);
                    if (!canUpload.allowed) {
                        errors.push({
                            filename: file.originalname,
                            error: canUpload.message
                        });
                        continue;
                    }
                    const fileBuffer = file.buffer;
                    const fileHash = crypto_1.default.createHash('sha256').update(fileBuffer).digest('hex');
                    const existingImage = await database_1.prisma.image.findUnique({
                        where: { fileHash }
                    });
                    if (existingImage) {
                        const publicId = await image_id_service_1.ImageIdService.generatePublicId(existingImage.id);
                        let systemUrl = existingImage.systemUrl;
                        if (!systemUrl.startsWith('/image/show/')) {
                            systemUrl = `/image/show/${publicId}`;
                            await database_1.prisma.image.update({
                                where: { id: existingImage.id },
                                data: { systemUrl }
                            });
                        }
                        const baseUrl = `${req.protocol}://${req.get('host')}`;
                        const fullSystemUrl = systemUrl.startsWith('http') ? systemUrl : `${baseUrl}${systemUrl}`;
                        results.push({
                            filename: file.originalname,
                            imageId: publicId,
                            systemUrl: fullSystemUrl,
                            isReused: true
                        });
                    }
                    else {
                        const imageInfo = await image_processing_service_1.ImageProcessingService.getImageInfo(fileBuffer, file.mimetype);
                        const fileName = `${fileHash}${path_1.default.extname(file.originalname)}`;
                        const filePath = await file_service_1.FileService.saveFile(fileBuffer, fileName);
                        const image = await database_1.prisma.image.create({
                            data: {
                                originalName: file.originalname,
                                fileHash,
                                fileMd5: crypto_1.default.createHash('md5').update(fileBuffer).digest('hex'),
                                fileSize: BigInt(file.size),
                                mimeType: file.mimetype,
                                width: imageInfo.width ?? null,
                                height: imageInfo.height ?? null,
                                systemUrl: `/api/images/${fileHash}`,
                                uploadStatus: 'processing',
                            }
                        });
                        const publicId = await image_id_service_1.ImageIdService.generatePublicId(image.id);
                        await database_1.prisma.image.update({
                            where: { id: image.id },
                            data: {
                                systemUrl: `/image/show/${publicId}`
                            }
                        });
                        await database_1.prisma.userImage.create({
                            data: {
                                userId: parseInt(userId),
                                imageId: image.id,
                                isOriginalUploader: true,
                            }
                        });
                        const baseUrl = `${req.protocol}://${req.get('host')}`;
                        const fullSystemUrl = `${baseUrl}/image/show/${publicId}`;
                        results.push({
                            filename: file.originalname,
                            imageId: publicId,
                            systemUrl: fullSystemUrl,
                            isReused: false
                        });
                        await queue_service_1.QueueService.addUploadJob({
                            imageId: image.id,
                            userId: parseInt(userId),
                            filePath,
                            fileInfo: {
                                originalName: file.originalname,
                                mimeType: file.mimetype,
                                size: file.size,
                                hash: fileHash,
                            }
                        });
                    }
                }
                catch (fileError) {
                    console.error(`处理文件 ${file.originalname} 时出错:`, fileError);
                    errors.push({
                        filename: file.originalname,
                        error: '文件处理失败'
                    });
                }
            }
            res.json({
                success: true,
                message: `批量上传完成，成功: ${results.length}，失败: ${errors.length}`,
                data: {
                    results,
                    errors,
                    summary: {
                        total: files.length,
                        success: results.length,
                        failed: errors.length
                    }
                }
            });
        }
        catch (error) {
            console.error('批量上传错误:', error);
            next(error);
        }
    }
    static async checkUploadLimits(userId, fileSize) {
        try {
            const user = await database_1.prisma.user.findUnique({
                where: { id: userId },
                select: { userLevel: true }
            });
            if (!user) {
                return {
                    allowed: false,
                    errorCode: 'USER_NOT_FOUND',
                    message: '用户不存在'
                };
            }
            const levelConfig = await database_1.prisma.userLevelConfig.findUnique({
                where: { level: user.userLevel }
            });
            if (!levelConfig) {
                return {
                    allowed: false,
                    errorCode: 'LEVEL_CONFIG_NOT_FOUND',
                    message: '用户等级配置不存在'
                };
            }
            if (fileSize > Number(levelConfig.maxFileSize)) {
                return {
                    allowed: false,
                    errorCode: types_1.ErrorCodes.FILE_TOO_LARGE,
                    message: `文件大小超过限制，最大允许 ${Math.round(Number(levelConfig.maxFileSize) / 1024 / 1024)}MB`
                };
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const todayUploads = await database_1.prisma.uploadLog.count({
                where: {
                    userId,
                    actionType: 'upload',
                    isSuccess: true,
                    createdAt: {
                        gte: today,
                        lt: tomorrow
                    }
                }
            });
            if (todayUploads >= levelConfig.maxDailyUploads) {
                return {
                    allowed: false,
                    errorCode: types_1.ErrorCodes.UPLOAD_LIMIT_EXCEEDED,
                    message: `今日上传次数已达上限 ${levelConfig.maxDailyUploads} 次`
                };
            }
            return { allowed: true };
        }
        catch (error) {
            console.error('检查上传限制时出错:', error);
            return {
                allowed: false,
                errorCode: types_1.ErrorCodes.INTERNAL_ERROR,
                message: '检查上传限制时出错'
            };
        }
    }
    static async logUpload(userId, imageId, actionType, req) {
        try {
            const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
            const userAgent = req.get('User-Agent') || '';
            await database_1.prisma.uploadLog.create({
                data: {
                    userId,
                    imageId,
                    actionType,
                    ipAddress: clientIP,
                    userAgent,
                    isSuccess: true,
                }
            });
        }
        catch (error) {
            console.error('记录上传日志失败:', error);
        }
    }
    static async getUploadLimits(req, res, next) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { userLevel: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'USER_NOT_FOUND',
                        message: '用户不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const levelConfig = await database_1.prisma.userLevelConfig.findUnique({
                where: { level: user.userLevel }
            });
            if (!levelConfig) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'LEVEL_CONFIG_NOT_FOUND',
                        message: '用户等级配置不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const todayUploads = await database_1.prisma.uploadLog.count({
                where: {
                    userId: parseInt(userId),
                    actionType: 'upload',
                    isSuccess: true,
                    createdAt: {
                        gte: today,
                        lt: tomorrow
                    }
                }
            });
            res.json({
                success: true,
                data: {
                    userLevel: user.userLevel,
                    maxDailyUploads: levelConfig.maxDailyUploads,
                    maxFileSize: Number(levelConfig.maxFileSize),
                    maxStorageSpace: Number(levelConfig.maxStorageSpace),
                    todayUploads,
                    remainingUploads: Math.max(0, levelConfig.maxDailyUploads - todayUploads),
                    canChooseProvider: levelConfig.canChooseProvider,
                    visibleProviderCount: levelConfig.visibleProviderCount,
                }
            });
        }
        catch (error) {
            console.error('获取上传限制信息错误:', error);
            next(error);
        }
    }
    static async getUploadHistory(req, res, next) {
        try {
            const userId = req.user?.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            const offset = (page - 1) * limit;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const [uploads, total] = await Promise.all([
                database_1.prisma.userImage.findMany({
                    where: {
                        userId: parseInt(userId),
                        image: {
                            isDeleted: false
                        }
                    },
                    include: {
                        image: {
                            include: {
                                imageLinks: {
                                    include: {
                                        provider: true
                                    }
                                }
                            }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip: offset,
                    take: limit,
                }),
                database_1.prisma.userImage.count({
                    where: {
                        userId: parseInt(userId),
                        image: {
                            isDeleted: false
                        }
                    }
                })
            ]);
            const baseUrl = `${req.protocol}://${req.get('host')}`;
            const uploadHistory = uploads.map(upload => {
                const publicId = image_id_service_1.ImageIdService.generatePublicId ?
                    image_id_service_1.ImageIdService.generatePublicId(upload.image.id) :
                    upload.image.id.toString();
                let fullSystemUrl = upload.image.systemUrl;
                if (!fullSystemUrl.startsWith('http')) {
                    fullSystemUrl = `${baseUrl}${fullSystemUrl}`;
                }
                return {
                    id: upload.image.id,
                    originalName: upload.image.originalName,
                    fileSize: parseInt(upload.image.fileSize.toString()),
                    mimeType: upload.image.mimeType,
                    systemUrl: fullSystemUrl,
                    uploadStatus: upload.image.uploadStatus,
                    isOriginalUploader: upload.isOriginalUploader,
                    accessCount: upload.accessCount,
                    createdAt: upload.createdAt.toISOString(),
                    links: [{
                            provider: '代理链接',
                            url: fullSystemUrl,
                            status: 'active',
                        }]
                };
            });
            res.json({
                success: true,
                data: {
                    uploads: uploadHistory,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit),
                    }
                }
            });
        }
        catch (error) {
            console.error('获取上传历史错误:', error);
            next(error);
        }
    }
    static async getUploadStats(req, res, next) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const [totalUploads, totalSize, todayUploads, thisMonthUploads] = await Promise.all([
                database_1.prisma.userImage.count({
                    where: { userId: parseInt(userId) }
                }),
                database_1.prisma.image.aggregate({
                    where: {
                        userImages: {
                            some: { userId: parseInt(userId) }
                        }
                    },
                    _sum: { fileSize: true }
                }),
                database_1.prisma.uploadLog.count({
                    where: {
                        userId: parseInt(userId),
                        actionType: 'upload',
                        isSuccess: true,
                        createdAt: {
                            gte: new Date(new Date().setHours(0, 0, 0, 0))
                        }
                    }
                }),
                database_1.prisma.uploadLog.count({
                    where: {
                        userId: parseInt(userId),
                        actionType: 'upload',
                        isSuccess: true,
                        createdAt: {
                            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                        }
                    }
                })
            ]);
            res.json({
                success: true,
                data: {
                    totalUploads,
                    totalSize: parseInt((totalSize._sum?.fileSize || 0n).toString()),
                    todayUploads,
                    thisMonthUploads,
                }
            });
        }
        catch (error) {
            console.error('获取上传统计错误:', error);
            next(error);
        }
    }
    static async deleteUpload(req, res, next) {
        try {
            const userId = req.user?.id;
            const imageId = parseInt(req.params.imageId);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const userImage = await database_1.prisma.userImage.findFirst({
                where: {
                    userId: parseInt(userId),
                    imageId,
                },
                include: {
                    image: true
                }
            });
            if (!userImage) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'IMAGE_NOT_FOUND',
                        message: '图片不存在或无权限访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            await database_1.prisma.image.update({
                where: { id: imageId },
                data: { isDeleted: true }
            });
            res.json({
                success: true,
                message: '图片删除成功'
            });
        }
        catch (error) {
            console.error('删除图片错误:', error);
            next(error);
        }
    }
    static async retryUpload(req, res, next) {
        try {
            const userId = req.user?.id;
            const imageId = parseInt(req.params.imageId);
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const userImage = await database_1.prisma.userImage.findFirst({
                where: {
                    userId: parseInt(userId),
                    imageId,
                },
                include: {
                    image: true
                }
            });
            if (!userImage) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'IMAGE_NOT_FOUND',
                        message: '图片不存在或无权限访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (userImage.image.uploadStatus !== 'failed') {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '只能重试失败的上传',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const filePath = `uploads/${userImage.image.fileHash}`;
            await queue_service_1.QueueService.addUploadJob({
                imageId,
                userId: parseInt(userId),
                filePath,
                fileInfo: {
                    originalName: userImage.image.originalName,
                    mimeType: userImage.image.mimeType,
                    size: Number(userImage.image.fileSize),
                    hash: userImage.image.fileHash,
                }
            });
            await database_1.prisma.image.update({
                where: { id: imageId },
                data: { uploadStatus: 'processing' }
            });
            res.json({
                success: true,
                message: '重试上传任务已添加到队列'
            });
        }
        catch (error) {
            console.error('重试上传错误:', error);
            next(error);
        }
    }
    static async getAvailableProviders(req, res) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const providers = await user_provider_service_1.UserProviderService.getAvailableProviders(parseInt(userId));
            const response = {
                success: true,
                data: {
                    providers: providers.map(provider => ({
                        id: provider.id,
                        name: provider.name,
                        description: provider.description,
                        status: provider.status,
                        priority: provider.priority,
                        maxFileSize: provider.maxFileSize,
                        supportedFormats: provider.supportedFormats,
                        isPremium: provider.isPremium,
                        costPerUpload: provider.costPerUpload,
                        isAvailable: provider.isAvailable,
                        source: provider.source
                    }))
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取用户可用接口失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户可用接口失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
}
exports.UploadController = UploadController;
//# sourceMappingURL=upload.controller.js.map