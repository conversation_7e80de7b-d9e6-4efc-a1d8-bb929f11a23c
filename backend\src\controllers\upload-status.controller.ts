/**
 * 上传状态控制器
 * 提供图片上传状态和备份进度的API接口
 */

import { Request, Response, NextFunction } from 'express';
import { UploadStatusService } from '../services/upload-status.service';
import { ApiResponse, ErrorCodes } from '../types';

export class UploadStatusController {
  /**
   * 获取图片的详细上传状态
   */
  static async getImageStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { imageId } = req.params;
      const userId = parseInt(req.user!.id);

      if (!imageId || isNaN(parseInt(imageId))) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的图片ID',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const status = await UploadStatusService.getImageUploadStatus(parseInt(imageId), userId);

      if (!status) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '图片不存在或无权限访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        message: '获取图片状态成功',
        data: status
      } as ApiResponse);

    } catch (error) {
      console.error('获取图片状态失败:', error);
      next(error);
    }
  }

  /**
   * 获取用户的上传历史（包含备份状态）
   */
  static async getUploadHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // 最大100条

      const result = await UploadStatusService.getUserUploadHistory(userId, page, limit);

      res.json({
        success: true,
        message: '获取上传历史成功',
        data: result
      } as ApiResponse);

    } catch (error) {
      console.error('获取上传历史失败:', error);
      next(error);
    }
  }

  /**
   * 获取图片的备份统计信息
   */
  static async getBackupStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { imageId } = req.params;
      const userId = parseInt(req.user!.id);

      if (!imageId || isNaN(parseInt(imageId))) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的图片ID',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 验证用户权限
      const status = await UploadStatusService.getImageUploadStatus(parseInt(imageId), userId);
      if (!status) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '图片不存在或无权限访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const stats = await UploadStatusService.getBackupStats(parseInt(imageId));

      res.json({
        success: true,
        message: '获取备份统计成功',
        data: stats
      } as ApiResponse);

    } catch (error) {
      console.error('获取备份统计失败:', error);
      next(error);
    }
  }

  /**
   * 获取系统的上传接口状态概览
   */
  static async getProvidersOverview(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = parseInt(req.user!.id);

      // 获取用户可用的接口
      const { UploadProviderService } = await import('../services/upload-provider.service');
      const { prisma } = await import('../config/database');

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { userLevel: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const providers = await UploadProviderService.getAvailableProviders(userId, user.userLevel);

      // 获取每个接口的统计信息
      const providersWithStats = await Promise.all(
        providers.map(async (provider) => {
          const linkCount = await prisma.imageLink.count({
            where: {
              providerId: provider.id,
              status: 'active'
            }
          });

          const avgResponseTime = await prisma.imageLink.aggregate({
            where: {
              providerId: provider.id,
              status: 'active',
              responseTime: { not: null }
            },
            _avg: {
              responseTime: true
            }
          });

          return {
            id: provider.id,
            name: provider.name,
            endpoint: provider.endpoint,
            status: 'active', // 这里可以添加实时健康检查
            priority: provider.isPremium ? 'high' : 'normal',
            totalLinks: linkCount,
            avgResponseTime: avgResponseTime._avg.responseTime || null,
            maxFileSize: provider.maxFileSize,
            supportedFormats: provider.supportedFormats
          };
        })
      );

      res.json({
        success: true,
        message: '获取接口概览成功',
        data: {
          providers: providersWithStats,
          summary: {
            totalProviders: providers.length,
            activeProviders: providers.length, // 这里可以添加实际的健康检查
            totalLinks: providersWithStats.reduce((sum, p) => sum + p.totalLinks, 0)
          }
        }
      } as ApiResponse);

    } catch (error) {
      console.error('获取接口概览失败:', error);
      next(error);
    }
  }
}
