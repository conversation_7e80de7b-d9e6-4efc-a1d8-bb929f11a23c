<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传设置测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 上传设置API测试页面</h1>
        <p>测试图片跳转配置API的各个端点</p>

        <!-- 登录测试 -->
        <div class="test-section">
            <h3>1. 用户登录</h3>
            <input type="email" id="email" placeholder="邮箱" value="<EMAIL>">
            <input type="password" id="password" placeholder="密码" value="Admin123">
            <button onclick="testLogin()">登录</button>
            <div id="loginResult" class="result"></div>
        </div>

        <!-- 获取提供商测试 -->
        <div class="test-section">
            <h3>2. 获取可用提供商</h3>
            <button onclick="testGetProviders()">获取提供商列表</button>
            <div id="providersResult" class="result"></div>
        </div>

        <!-- 获取配置测试 -->
        <div class="test-section">
            <h3>3. 获取用户配置</h3>
            <button onclick="testGetConfig()">获取当前配置</button>
            <div id="configResult" class="result"></div>
        </div>

        <!-- 保存配置测试 -->
        <div class="test-section">
            <h3>4. 保存用户配置</h3>
            <select id="redirectMode">
                <option value="auto">自动最优</option>
                <option value="manual">手动指定</option>
            </select>
            <select id="providerId">
                <option value="">请选择提供商</option>
            </select>
            <label>
                <input type="checkbox" id="isActive" checked> 启用配置
            </label>
            <br><br>
            <button onclick="testSaveConfig()">保存配置</button>
            <div id="saveResult" class="result"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>5. 综合测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="allTestsResult" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        let availableProviders = [];

        // 登录测试
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.token;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登录成功！\nToken: ${authToken.substring(0, 20)}...`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登录失败: ${data.error?.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 获取提供商测试
        async function testGetProviders() {
            const resultDiv = document.getElementById('providersResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先登录';
                return;
            }

            try {
                const response = await fetch('/api/image-redirect-config/providers', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    availableProviders = data.data.providers || [];
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取提供商成功！\n${JSON.stringify(data, null, 2)}`;
                    
                    // 更新提供商下拉列表
                    const providerSelect = document.getElementById('providerId');
                    providerSelect.innerHTML = '<option value="">请选择提供商</option>';
                    availableProviders.forEach(provider => {
                        const option = document.createElement('option');
                        option.value = provider.id;
                        option.textContent = `${provider.displayName || provider.name} (ID: ${provider.id})`;
                        providerSelect.appendChild(option);
                    });
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取提供商失败: ${data.error?.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 获取配置测试
        async function testGetConfig() {
            const resultDiv = document.getElementById('configResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先登录';
                return;
            }

            try {
                const response = await fetch('/api/image-redirect-config/global', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取配置成功！\n${JSON.stringify(data, null, 2)}`;
                    
                    // 更新表单
                    document.getElementById('redirectMode').value = data.data.redirectMode || 'auto';
                    document.getElementById('providerId').value = data.data.preferredProviderId || '';
                    document.getElementById('isActive').checked = data.data.isActive !== false;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取配置失败: ${data.error?.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 保存配置测试
        async function testSaveConfig() {
            const resultDiv = document.getElementById('saveResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先登录';
                return;
            }

            const redirectMode = document.getElementById('redirectMode').value;
            const providerId = document.getElementById('providerId').value;
            const isActive = document.getElementById('isActive').checked;

            const configData = {
                redirectMode,
                isActive
            };

            if (redirectMode === 'manual' && providerId) {
                configData.preferredProviderId = parseInt(providerId);
            }

            try {
                const response = await fetch('/api/image-redirect-config/global', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(configData)
                });

                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 保存配置成功！\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 保存配置失败: ${data.error?.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 运行所有测试
        async function runAllTests() {
            const resultDiv = document.getElementById('allTestsResult');
            resultDiv.className = 'result';
            resultDiv.textContent = '🔄 正在运行所有测试...';

            const tests = [
                { name: '登录', func: testLogin },
                { name: '获取提供商', func: testGetProviders },
                { name: '获取配置', func: testGetConfig }
            ];

            let results = [];
            
            for (const test of tests) {
                try {
                    await test.func();
                    results.push(`✅ ${test.name}: 成功`);
                    await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
            }

            resultDiv.className = 'result success';
            resultDiv.textContent = `🎉 所有测试完成！\n\n${results.join('\n')}`;
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('📋 上传设置API测试页面已加载');
            console.log('💡 请按顺序执行测试：登录 → 获取提供商 → 获取配置 → 保存配置');
        };
    </script>
</body>
</html>
